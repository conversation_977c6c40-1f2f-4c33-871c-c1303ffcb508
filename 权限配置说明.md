# CommonService 权限配置说明

## 权限验证模式

CommonService 现在支持三种权限验证模式，通过 `authMode` 参数配置：

### 1. merchant 模式（默认）
- **适用场景**：基于商户权限的数据访问控制
- **权限逻辑**：用户只能访问自己创建的商户下的数据
- **适用模块**：material、human、hot、pain、Qa、video

```typescript
// 使用默认的 merchant 模式
async findMaterial(filter: Partial<Material>): Promise<ZtBaseResDto> {
  return this.findWithPagination(
    this.materialRepository,
    filter,
    MerchantResDto,
    // 不需要指定 authMode，默认为 'merchant'
  );
}

// 或者显式指定
async findMaterial(filter: Partial<Material>): Promise<ZtBaseResDto> {
  return this.findWithPagination(
    this.materialRepository,
    filter,
    MerchantResDto,
    { authMode: 'merchant' }
  );
}
```

### 2. createdBy 模式
- **适用场景**：基于创建者的数据访问控制
- **权限逻辑**：用户只能访问自己创建的数据
- **适用模块**：benchmarking、ip

```typescript
// benchmarking 模块示例
async findBenchmarking(filter: Partial<BenchmarkingReqDto>): Promise<ZtBaseResDto> {
  return this.findWithPagination(
    this.benchmarkingRepository,
    filter,
    BenchmarkingResDto,
    { authMode: 'createdBy' }
  );
}

// ip 模块示例
async find(filter: Partial<IpFindDto>): Promise<ZtBaseResDto> {
  return this.findWithPagination(
    this.ipRepository,
    filter,
    IpFindDtoRes,
    { 
      authMode: 'createdBy',
      relations: ['merchants']
    }
  );
}

async findAll(filter: Partial<IpFindDtoRes> = {}): Promise<IpEntity[]> {
  return this.findAllEntities(
    this.ipRepository,
    filter,
    { sortBy: 'createdAt', sortOrder: 'descend' },
    { 
      authMode: 'createdBy',
      relations: ['merchants']
    }
  );
}
```

### 3. none 模式
- **适用场景**：无权限验证，查询所有数据
- **权限逻辑**：不进行任何权限过滤
- **适用模块**：公共数据或管理员专用功能

```typescript
// 使用 none 模式（谨慎使用）
async findAllPublicData(filter: any): Promise<ZtBaseResDto> {
  return this.findWithPagination(
    this.repository,
    filter,
    ResDto,
    { authMode: 'none' }
  );
}
```

## 权限验证原理

### merchant 模式原理：
1. 从当前用户上下文获取用户关联的商户列表 `currentUser.merchants`
2. 如果用户没有关联商户，则查询用户创建的商户（`createdBy` 字段）
3. 在查询条件中添加 `merchantId IN (userMerchantIds)` 过滤
4. 确保用户只能访问有权限的商户数据

### createdBy 模式原理：
1. 从当前用户上下文获取用户ID
2. 在查询条件中添加 `createdBy = userId` 过滤
3. 确保用户只能访问自己创建的数据

### none 模式原理：
- 跳过所有权限验证，直接查询数据

## 使用建议

1. **默认使用 merchant 模式**：大多数业务数据都应该基于商户权限
2. **谨慎使用 createdBy 模式**：仅在确实需要基于创建者权限时使用
3. **极少使用 none 模式**：仅在公共数据或特殊场景下使用

## 配置示例

```typescript
// findWithPagination 方法配置
return this.findWithPagination(
  repository,
  filter,
  EntityResDto,
  {
    relations: ['relatedEntity'], // 关联查询
    authMode: 'createdBy'         // 权限模式
  }
);

// findAllEntities 方法配置
return this.findAllEntities(
  repository,
  filter,
  { sortBy: 'createdAt', sortOrder: 'descend' },
  {
    relations: ['relatedEntity'], // 关联查询
    authMode: 'merchant',         // 权限模式
    needMerchantAuth: true        // 强制启用商户权限（在 merchant 模式下）
  }
);
``` 