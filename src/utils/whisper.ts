import { exec } from 'child_process';
import * as util from 'util';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import ffmpeg from 'fluent-ffmpeg';

export class WhisperService {
  private execPromise = util.promisify(exec);
  private readonly tempDir = path.join(process.cwd(), 'temp');
  private readonly maxRetries = 3;

  constructor() {
    // 创建临时目录
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  /**
   * 音频转文字
   * @param audioUrl 音频URL
   */
  async transcribe(audioUrl: string): Promise<string> {
    try {
      // 1. 下载音频
      const audioPath = await this.downloadAudio(audioUrl);

      // 2. 转换音频格式
      const wavPath = await this.convertToWav(audioPath);

      // 3. 使用whisper转写
      const text = await this.runWhisper(wavPath);

      // 4. 清理临时文件
      this.cleanupFiles([audioPath, wavPath]);

      return text;
    } catch (error) {
      throw new Error(`Whisper转换失败: ${error.message}`);
    }
  }

  /**
   * 下载音频文件
   */
  private async downloadAudio(url: string): Promise<string> {
    const tempFile = path.join(this.tempDir, `${Date.now()}.mp3`);

    try {
      const response = await axios({
        method: 'GET',
        url: url,
        responseType: 'stream',
      });

      const writer = fs.createWriteStream(tempFile);
      response.data.pipe(writer);

      return new Promise((resolve, reject) => {
        writer.on('finish', () => resolve(tempFile));
        writer.on('error', reject);
      });
    } catch (error) {
      throw new Error(`音频下载失败: ${error.message}`);
    }
  }

  /**
   * 转换音频格式为WAV
   */
  private async convertToWav(inputPath: string): Promise<string> {
    const outputPath = path.join(this.tempDir, `${Date.now()}.wav`);

    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .toFormat('wav')
        .audioFrequency(16000) // 16kHz 采样率
        .audioChannels(1) // 单声道
        .on('end', () => resolve(outputPath))
        .on('error', (err) => reject(new Error(`音频转换失败: ${err.message}`)))
        .save(outputPath);
    });
  }

  /**
   * 运行Whisper进行转写
   */
  private async runWhisper(audioPath: string): Promise<string> {
    let retries = 0;

    while (retries < this.maxRetries) {
      try {
        const { stdout } = await this.execPromise(
          `whisper ${audioPath} --model base --language zh --task transcribe --output_dir ${this.tempDir}`,
        );

        // 读取生成的文本文件
        const textFilePath = audioPath.replace('.wav', '.txt');
        if (fs.existsSync(textFilePath)) {
          const text = fs.readFileSync(textFilePath, 'utf-8');
          fs.unlinkSync(textFilePath); // 删除文本文件
          return text.trim();
        }

        return stdout.trim();
      } catch (error) {
        retries++;
        if (retries === this.maxRetries) {
          throw error;
        }
        // 等待后重试
        await new Promise((resolve) => setTimeout(resolve, 1000 * retries));
      }
    }

    throw new Error('Whisper转写失败：达到最大重试次数');
  }

  /**
   * 清理临时文件
   */
  private cleanupFiles(files: string[]): void {
    files.forEach((file) => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
      }
    });
  }
}
