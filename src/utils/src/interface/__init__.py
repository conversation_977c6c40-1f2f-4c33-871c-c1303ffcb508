from ..interface.account import Account
from ..interface.account_tiktok import Account<PERSON><PERSON><PERSON>ok
from ..interface.collection import Collection
from ..interface.collects import (
    Collects,
    CollectsDetail,
    CollectsMix,
    CollectsMusic,
    CollectsSeries,
)
from ..interface.comment import Comment, Reply
from ..interface.comment_tiktok import CommentTikTok, ReplyTikTok
from ..interface.detail import Detail
from ..interface.detail_tiktok import DetailTikTok
from ..interface.hashtag import HashTag
from ..interface.hot import Hot
from ..interface.info import Info
from ..interface.info_tiktok import InfoTikTok
from ..interface.live import Live
from ..interface.live_tiktok import LiveTikTok
from ..interface.mix import Mix
from ..interface.mix_tiktok import Mix<PERSON>istTikTok
from ..interface.mix_tiktok import MixTikTok
from ..interface.search import Search
from ..interface.template import API
from ..interface.template import APITikTok
from ..interface.user import User
