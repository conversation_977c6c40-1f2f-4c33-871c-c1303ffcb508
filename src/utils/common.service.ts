import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import {
  DeepPartial,
  FindOptionsOrder,
  FindOptionsWhere,
  In,
  Repository,
} from 'typeorm';
import { ZtBaseResDto } from './baseRes.dto';
import { filterData } from './common';
import { RemoveReqDto } from './remove.req.dto';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { reqUser } from './nameSpace';

@Injectable()
export class CommonService {
  constructor(@Inject(REQUEST) protected readonly request: Request) {}

  // Generic methods
  protected async findWithPagination<T extends object>(
    repository: Repository<T>,
    filter: any,
    entityType: any,
    options: { 
      relations?: string[];
      authMode?: 'project' | 'createdBy' | 'none';
    } = {},
  ): Promise<ZtBaseResDto> {
    try {
      const { pageIndex = 1, pageSize = 10, sort } = filter;
      const currentUser = this.request[reqUser];

      // 确定权限验证模式
      const authMode = options.authMode || 'project'; // 默认为project模式
      const userId = currentUser?.id;

      // 根据权限模式进行不同的权限检查
      if (authMode === 'none') {
        // 无权限验证模式，跳过所有权限检查
      } else if (authMode === 'createdBy') {
        // 基于createdBy的权限验证
        if (!userId) {
          return new ZtBaseResDto(filter, [[], 0], entityType);
        }
        // createdBy模式下，直接在查询条件中添加createdBy过滤
      } else if (authMode === 'project') {
        // 基于projectId的权限验证（原逻辑）
        let userProjectIds = currentUser?.projects?.map((m) => m.id) || [];
        
        // 如果用户没有关联项目，则查询用户创建的项目
        if (!userProjectIds.length && userId) {
          const projectRepository = repository.manager.getRepository('Project');
          const userCreatedProjects = await projectRepository.find({
            where: { createdBy: userId },
            select: ['id']
          });
          userProjectIds = userCreatedProjects.map(m => m.id);
        }
        
        // 如果系统需要项目权限验证但用户没有关联项目，则返回空结果
        const needsProjectCheck = repository.metadata.findColumnWithPropertyName('merchantId');
        if (needsProjectCheck && !userProjectIds.length) {
          return new ZtBaseResDto(filter, [[], 0], entityType);
        }
        
        // 将projectIds存储起来，后面使用
        (filter as any)._userProjectIds = userProjectIds;
      }

      const defaultSort = { sortBy: 'createdAt', sortOrder: 'descend' };
      const sortConfig = sort || defaultSort;

      const metadata = repository.metadata;
      const column = metadata.findColumnWithPropertyName(sortConfig.sortBy);
      if (!column) {
        throw new Error(`Invalid sort field: ${sortConfig.sortBy}`);
      }

      const order: FindOptionsOrder<T> = {
        [sortConfig.sortBy]:
          sortConfig.sortOrder === 'descend' ? 'DESC' : 'ASC',
      } as FindOptionsOrder<T>;

      // 使用filterData函数处理过滤条件
      const whereCondition: Record<string, any> = {
        ...filterData(filter, entityType),
      };

      // 特殊处理ipId字段
      if (filter.ipId) {
        whereCondition.ipId = filter.ipId;
        console.log(`应用ipId过滤条件: ${filter.ipId}`);
      }

      // 处理权限过滤
      if (authMode === 'createdBy') {
        // 基于createdBy的权限验证
        whereCondition.createdBy = userId;
      } else if (authMode === 'project') {
        // 基于projectId的权限验证
        const userProjectIds = (filter as any)._userProjectIds;
        const needsProjectCheck = repository.metadata.findColumnWithPropertyName('merchantId');
        
        if (needsProjectCheck && userProjectIds) {
          // 如果前端传入了merchantId参数
          if (filter.merchantId) {
            // 确保用户只能查询有权限的项目数据
            const requestedProjectIds = Array.isArray(filter.merchantId)
              ? filter.merchantId
              : [filter.merchantId];

            // 找出用户有权限的项目ID交集
            const allowedProjectIds = requestedProjectIds.filter((id) =>
              userProjectIds.includes(id),
            );

            if (!allowedProjectIds.length) {
              return new ZtBaseResDto(filter, [[], 0], entityType);
            }

            whereCondition.merchantId = In(allowedProjectIds);
          } else {
            // 如果没有指定merchantId，则查询用户所有有权限的项目数据
            whereCondition.merchantId = In(userProjectIds);
          }
        }
        
        // 清理临时变量
        delete (filter as any)._userProjectIds;
      }

      console.log('最终查询条件:', JSON.stringify(whereCondition));

      const res = await repository.findAndCount({
        where: whereCondition as FindOptionsWhere<T>,
        skip: (pageIndex - 1) * pageSize,
        take: pageSize,
        order,
        relations: options.relations,
      });

      return new ZtBaseResDto(filter, res, entityType);
    } catch (error) {
      throw new HttpException(
        `Query failed: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // Generic update method
  protected async updateEntity<T>(
    repository: Repository<T>,
    id: string,
    updateDto: Partial<T>,
    entityName: string,
  ): Promise<void> {
    try {
      const entity = await repository.findOne({ where: { id } as any });
      if (!entity) {
        throw new HttpException(
          `${entityName} with id ${id} not found`,
          HttpStatus.NOT_FOUND,
        );
      }

      // 获取当前用户ID
      const currentUser = this.request[reqUser];
      const userId = currentUser?.id;

      await repository.save({
        ...entity,
        ...updateDto,
        updatedBy: userId || 'system',
      });
    } catch (error) {
      throw new HttpException(
        `Update ${entityName} failed: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // Generic create method
  protected async createEntity<T>(
    repository: Repository<T>,
    entity: DeepPartial<T> | DeepPartial<T>[],
    entityName: string,
  ): Promise<void> {
    try {
      // 获取当前用户ID
      const currentUser = this.request[reqUser];
      const userId = currentUser?.id;

      console.log(6666, currentUser);

      if (Array.isArray(entity)) {
        const entities = repository.create(
          entity.map((e) => ({
            ...e,
            createdBy: userId || 'system',
            updatedBy: userId || 'system',
          })),
        );
        await repository.save(entities);
      } else {
        const newEntity = repository.create({
          ...entity,
          createdBy: userId || 'system',
          updatedBy: userId || 'system',
        } as DeepPartial<T>);
        await repository.save(newEntity);
      }
    } catch (error) {
      if (error?.code === 'ER_DUP_ENTRY') {
        throw new HttpException(
          `创建${entityName}失败: 数据已存在`,
          HttpStatus.BAD_REQUEST,
        );
      }
      throw new HttpException(
        `创建${entityName}失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // Generic delete method
  protected async deleteEntity<T>(
    repository: Repository<T>,
    params: RemoveReqDto,
    entityName: string,
  ): Promise<void> {
    try {
      // 获取当前用户信息
      const currentUser = this.request[reqUser];
      const userId = currentUser?.id;

      if (!userId) {
        throw new HttpException(
          `删除${entityName}失败: 未授权用户`,
          HttpStatus.UNAUTHORIZED,
        );
      }
      
      // 检查实体是否有createdBy字段
      const metadata = repository.metadata;
      const hasCreatedByField = metadata.findColumnWithPropertyName('createdBy');

      if (hasCreatedByField) {
        // 检查用户是否正在尝试删除自己创建的记录
        if (params.idList && params.idList.length > 0) {
          // 查找所有记录以检查创建者
          const entities = await repository.find({
            where: { id: In(params.idList) } as any,
          });
          
          // 筛选出用户创建的记录
          const userCreatedIds = entities
            .filter((entity: any) => entity.createdBy === userId)
            .map((entity: any) => entity.id);
            
          if (userCreatedIds.length === 0) {
            throw new HttpException(
              `删除${entityName}失败: 您没有权限删除这些记录`,
              HttpStatus.FORBIDDEN,
            );
          }
          
          // 只删除用户创建的记录
          await repository.delete(userCreatedIds);
        } else if (params.id) {
          // 查找单个记录以检查创建者
          const entity = await repository.findOne({
            where: { id: params.id } as any,
          });
          
          if (!entity) {
            throw new HttpException(
              `${entityName}不存在`,
              HttpStatus.NOT_FOUND,
            );
          }
          
          // 检查创建者
          if ((entity as any).createdBy !== userId) {
            throw new HttpException(
              `删除${entityName}失败: 您只能删除自己创建的记录`,
              HttpStatus.FORBIDDEN,
            );
          }
          
          // 删除记录
          await repository.delete(params.id);
        }
      } else {
        // 如果实体没有createdBy字段，则按原方式删除
        if (params.idList && params.idList.length > 0) {
          await repository.delete(params.idList);
        } else {
          await repository.delete(params.id);
        }
      }
    } catch (error) {
      throw new HttpException(
        `删除${entityName}失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // 在 CozeServer 类中添加通用的 findAll 方法
  /**
   * 通用查询所有实体的方法
   * @param repository - TypeORM 仓库实例
   * @param filter - 查询过滤条件，可选，默认为空对象
   * @param sort - 排序配置对象，包含排序字段和排序方向
   * @param options - 查询选项，包含关联关系配置
   * @returns 返回查询到的实体数组
   * @throws HttpException 当查询失败时抛出异常
   */
  protected async findAllEntities<T>(
    repository: Repository<T>,
    filter: Partial<T> = {},
    sort: { sortBy?: string; sortOrder?: 'ascend' | 'descend' } = {
      sortBy: 'createdAt',
      sortOrder: 'descend',
    },
    options: { 
      relations?: string[]; 
      needProjectAuth?: boolean;
      authMode?: 'project' | 'createdBy' | 'none';
    } = {},
  ): Promise<T[]> {
    try {
      // 获取当前请求的用户信息
      const currentUser = this.request[reqUser];

      // 确定权限验证模式
      const authMode = options.authMode || (options.needProjectAuth ? 'project' : 'project');
      const userId = currentUser?.id;

      // 根据权限模式进行权限检查
      if (authMode === 'none') {
        // 无权限验证模式，跳过所有权限检查
      } else if (authMode === 'createdBy') {
        // 基于createdBy的权限验证
        if (!userId) {
          return [];
        }
      } else if (authMode === 'project') {
        // 基于projectId的权限验证
        let userProjectIds = currentUser?.projects?.map((m) => m.id) || [];
        
        // 如果用户没有关联项目，则查询用户创建的项目
        if (!userProjectIds.length && userId) {
          const projectRepository = repository.manager.getRepository('Project');
          const userCreatedProjects = await projectRepository.find({
            where: { createdBy: userId },
            select: ['id']
          });
          userProjectIds = userCreatedProjects.map(m => m.id);
        }

        // 检查是否需要项目权限校验
        const metadata = repository.metadata;
        const hasProjectId = metadata.findColumnWithPropertyName('merchantId');
        const needProjectAuth = options.needProjectAuth ?? hasProjectId;

        // 如果需要项目权限校验且用户没有关联项目，直接返回空数组
        if (needProjectAuth && !userProjectIds.length) {
          return [];
        }
        
        // 将projectIds存储起来，后面使用
        (filter as any)._userProjectIds = userProjectIds;
      }

      // 设置默认排序配置
      const defaultSort = { sortBy: 'createdAt', sortOrder: 'descend' };
      const sortConfig = sort || defaultSort;

      // 验证排序字段是否存在
      const metadata = repository.metadata;
      const column = metadata.findColumnWithPropertyName(sortConfig.sortBy);
      if (!column) {
        console.warn(`排序字段 ${sortConfig.sortBy} 不存在，使用默认排序`);
        sortConfig.sortBy = 'createdAt';
      }

      // 构建排序条件
      const order: FindOptionsOrder<T> = {
        [sortConfig.sortBy]:
          sortConfig.sortOrder === 'descend' ? 'DESC' : 'ASC',
      } as FindOptionsOrder<T>;

      // 使用 filterData 处理过滤条件
      const whereCondition: Record<string, any> = {
        ...filterData(filter, repository.target),
      };

      // 处理权限过滤
      if (authMode === 'createdBy') {
        // 基于createdBy的权限验证
        whereCondition.createdBy = userId;
      } else if (authMode === 'project') {
        // 基于projectId的权限验证
        const userProjectIds = (filter as any)._userProjectIds;
        const metadata = repository.metadata;
        const hasProjectId = metadata.findColumnWithPropertyName('merchantId');
        const needProjectAuth = options.needProjectAuth ?? hasProjectId;
        
        // 只有在需要项目权限校验时才处理项目权限过滤
        if (needProjectAuth && userProjectIds) {
          if (filter['merchantId']) {
            // 处理单个或多个项目ID
            const requestedProjectIds = Array.isArray(filter['merchantId'])
              ? filter['merchantId']
              : [filter['merchantId']];

            // 获取用户有权限的项目ID交集
            const allowedProjectIds = requestedProjectIds.filter((id) =>
              userProjectIds.includes(id),
            );

            // 如果没有权限访问任何请求的项目，返回空数组
            if (!allowedProjectIds.length) {
              return [];
            }

            whereCondition.merchantId = In(allowedProjectIds);
          } else {
            // 如果没有指定项目ID，则查询用户所有有权限的项目数据
            whereCondition.merchantId = In(userProjectIds);
          }
        }
        
        // 清理临时变量
        delete (filter as any)._userProjectIds;
      }

      // 执行查询并返回结果
      return await repository.find({
        where: whereCondition as FindOptionsWhere<T>,
        order,
        relations: options.relations,
      });
    } catch (error) {
      // 记录错误日志并抛出 HTTP 异常
      console.error('查询错误:', error);
      throw new HttpException(
        `查询失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
