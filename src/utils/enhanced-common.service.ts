import { HttpException, HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import {
  DeepPartial,
  FindOptionsOrder,
  FindOptionsWhere,
  In,
  Repository,
} from 'typeorm';
import { ZtBaseResDto } from './baseRes.dto';
import { filterData } from './common';
import { RemoveReqDto } from './remove.req.dto';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { reqUser } from './nameSpace';
import { 
  EntityNotFoundException, 
  ValidationException, 
  InsufficientPermissionException 
} from '../common/exceptions/business.exception';

export interface ServiceOptions {
  relations?: string[];
  authMode?: 'project' | 'createdBy' | 'none';
  validateOwnership?: boolean;
  softDelete?: boolean;
}

@Injectable()
export class EnhancedCommonService {
  protected readonly logger = new Logger(this.constructor.name);
  
  constructor(@Inject(REQUEST) protected readonly request: Request) {}

  /**
   * 增强的分页查询方法
   */
  protected async findWithPaginationEnhanced<T extends object>(
    repository: Repository<T>,
    filter: any,
    entityType: any,
    options: ServiceOptions = {},
  ): Promise<ZtBaseResDto> {
    try {
      const startTime = Date.now();
      
      const { pageIndex = 1, pageSize = 10, sort } = filter;
      const currentUser = this.request[reqUser];

      // 权限验证
      this.validateUserPermission(currentUser, options.authMode);

      // 构建查询条件
      const whereConditions = this.buildWhereConditions(filter, currentUser, options.authMode);

      // 构建排序条件
      const orderConditions = this.buildOrderConditions(sort);

      // 执行查询
      const [data, total] = await repository.findAndCount({
        where: whereConditions,
        order: orderConditions,
        skip: (pageIndex - 1) * pageSize,
        take: pageSize,
        relations: options.relations || [],
      });

      const duration = Date.now() - startTime;
      
      // 记录性能日志
      if (duration > 1000) {
        this.logger.warn(`Slow query detected: ${duration}ms for ${entityType.name}`);
      }

      const result = new ZtBaseResDto(filter, [data, total], entityType);
      
      this.logger.debug(`Query completed: ${data.length}/${total} records in ${duration}ms`);
      
      return result;
    } catch (error) {
      this.logger.error(`Failed to find ${entityType.name} with pagination`, error.stack);
      throw error;
    }
  }

  /**
   * 增强的实体创建方法
   */
  protected async createEntityEnhanced<T extends object>(
    repository: Repository<T>,
    data: DeepPartial<T>,
    entityName: string,
    options: ServiceOptions = {},
  ): Promise<T> {
    try {
      const currentUser = this.request[reqUser];
      
      // 权限验证
      this.validateUserPermission(currentUser, options.authMode);

      // 数据验证
      this.validateEntityData(data, 'create');

      // 设置创建者信息
      if (options.authMode !== 'none' && currentUser) {
        (data as any).createdBy = currentUser.id;
      }

      const entity = repository.create(data);
      const savedEntity = await repository.save(entity);

      this.logger.log(`${entityName} created successfully with id: ${(savedEntity as any).id}`);
      
      return savedEntity;
    } catch (error) {
      this.logger.error(`Failed to create ${entityName}`, error.stack);
      throw error;
    }
  }

  /**
   * 增强的实体更新方法
   */
  protected async updateEntityEnhanced<T extends object>(
    repository: Repository<T>,
    id: string,
    data: DeepPartial<T>,
    entityName: string,
    options: ServiceOptions = {},
  ): Promise<T> {
    try {
      const currentUser = this.request[reqUser];
      
      // 权限验证
      this.validateUserPermission(currentUser, options.authMode);

      // 检查实体是否存在
      const existingEntity = await repository.findOne({
        where: { id } as any,
        relations: options.relations || [],
      });

      if (!existingEntity) {
        throw new EntityNotFoundException(entityName, id);
      }

      // 所有权验证
      if (options.validateOwnership) {
        this.validateEntityOwnership(existingEntity, currentUser, options.authMode);
      }

      // 数据验证
      this.validateEntityData(data, 'update');

      // 设置更新者信息
      if (options.authMode !== 'none' && currentUser) {
        (data as any).updatedBy = currentUser.id;
      }

      await repository.update(id, data as any);
      
      const updatedEntity = await repository.findOne({
        where: { id } as any,
        relations: options.relations || [],
      });

      this.logger.log(`${entityName} updated successfully with id: ${id}`);
      
      return updatedEntity!;
    } catch (error) {
      this.logger.error(`Failed to update ${entityName} with id: ${id}`, error.stack);
      throw error;
    }
  }

  /**
   * 增强的实体删除方法
   */
  protected async deleteEntityEnhanced<T extends object>(
    repository: Repository<T>,
    params: RemoveReqDto,
    entityName: string,
    options: ServiceOptions = {},
  ): Promise<void> {
    try {
      const { id } = params;
      const currentUser = this.request[reqUser];
      
      // 权限验证
      this.validateUserPermission(currentUser, options.authMode);

      // 检查实体是否存在
      const existingEntity = await repository.findOne({
        where: { id } as any,
      });

      if (!existingEntity) {
        throw new EntityNotFoundException(entityName, id);
      }

      // 所有权验证
      if (options.validateOwnership) {
        this.validateEntityOwnership(existingEntity, currentUser, options.authMode);
      }

      if (options.softDelete) {
        // 软删除
        await repository.update(id, { 
          deletedAt: new Date(),
          deletedBy: currentUser?.id 
        } as any);
      } else {
        // 硬删除
        await repository.delete(id);
      }

      this.logger.log(`${entityName} deleted successfully with id: ${id}`);
    } catch (error) {
      this.logger.error(`Failed to delete ${entityName} with id: ${params.id}`, error.stack);
      throw error;
    }
  }

  /**
   * 验证用户权限
   */
  private validateUserPermission(currentUser: any, authMode?: string): void {
    if (authMode === 'none') return;
    
    if (!currentUser) {
      throw new InsufficientPermissionException('access resource', 'user not authenticated');
    }
  }

  /**
   * 验证实体所有权
   */
  private validateEntityOwnership(entity: any, currentUser: any, authMode?: string): void {
    if (authMode === 'none') return;
    
    if (authMode === 'createdBy' && entity.createdBy !== currentUser?.id) {
      throw new InsufficientPermissionException('access resource', 'not owner');
    }
    
    if (authMode === 'project') {
      const userProjectIds = currentUser?.projects?.map((m: any) => m.id) || [];
      if (!userProjectIds.includes(entity.merchantId)) {
        throw new InsufficientPermissionException('access resource', 'not in project scope');
      }
    }
  }

  /**
   * 构建查询条件
   */
  private buildWhereConditions(filter: any, currentUser: any, authMode?: string): any {
    const whereConditions: any = {};
    
    // 权限过滤
    if (authMode === 'createdBy' && currentUser?.id) {
      whereConditions.createdBy = currentUser.id;
    } else if (authMode === 'project' && currentUser?.projects) {
      const projectIds = currentUser.projects.map((m: any) => m.id);
      if (projectIds.length > 0) {
        whereConditions.merchantId = In(projectIds);
      }
    }

    // 业务过滤条件
    Object.keys(filter).forEach(key => {
      if (!['pageIndex', 'pageSize', 'sort'].includes(key) && filter[key] !== undefined) {
        whereConditions[key] = filter[key];
      }
    });

    return whereConditions;
  }

  /**
   * 构建排序条件
   */
  private buildOrderConditions(sort?: any): FindOptionsOrder<any> {
    const defaultOrder = { createdAt: 'DESC' } as FindOptionsOrder<any>;
    
    if (!sort) return defaultOrder;
    
    if (sort.sortBy && sort.sortOrder) {
      return { [sort.sortBy]: sort.sortOrder.toUpperCase() } as FindOptionsOrder<any>;
    }
    
    return defaultOrder;
  }

  /**
   * 验证实体数据
   */
  private validateEntityData(data: any, operation: 'create' | 'update'): void {
    if (!data || typeof data !== 'object') {
      throw new ValidationException('Invalid data format');
    }
    
    // 可以在这里添加更多的数据验证逻辑
    if (operation === 'create' && !data.name && !data.projectName) {
      // 某些实体需要名称字段
      // throw new ValidationException('Name is required for creation');
    }
  }
}