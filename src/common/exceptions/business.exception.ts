import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * 业务异常基类
 */
export class BusinessException extends HttpException {
  constructor(
    message: string,
    code?: string,
    statusCode: HttpStatus = HttpStatus.BAD_REQUEST
  ) {
    const response = {
      code: code || 'BUSINESS_ERROR',
      message,
      timestamp: new Date().toISOString(),
    };
    super(response, statusCode);
  }
}

/**
 * 数据不存在异常
 */
export class EntityNotFoundException extends BusinessException {
  constructor(entityName: string, id?: string) {
    const message = id 
      ? `${entityName} with id ${id} not found`
      : `${entityName} not found`;
    super(message, 'ENTITY_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

/**
 * 数据验证异常
 */
export class ValidationException extends BusinessException {
  constructor(message: string, field?: string) {
    const fullMessage = field ? `${field}: ${message}` : message;
    super(fullMessage, 'VALIDATION_ERROR', HttpStatus.BAD_REQUEST);
  }
}

/**
 * 权限不足异常
 */
export class InsufficientPermissionException extends BusinessException {
  constructor(action: string, resource?: string) {
    const message = resource 
      ? `Insufficient permission to ${action} ${resource}`
      : `Insufficient permission to ${action}`;
    super(message, 'INSUFFICIENT_PERMISSION', HttpStatus.FORBIDDEN);
  }
}

/**
 * 外部服务异常
 */
export class ExternalServiceException extends BusinessException {
  constructor(serviceName: string, originalError?: any) {
    const message = `External service ${serviceName} error: ${originalError?.message || 'Unknown error'}`;
    super(message, 'EXTERNAL_SERVICE_ERROR', HttpStatus.SERVICE_UNAVAILABLE);
  }
}

/**
 * 工作流执行异常
 */
export class WorkflowExecutionException extends BusinessException {
  constructor(workflowName: string, error?: any) {
    const message = `Workflow ${workflowName} execution failed: ${error?.message || 'Unknown error'}`;
    super(message, 'WORKFLOW_EXECUTION_ERROR', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}