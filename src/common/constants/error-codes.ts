/**
 * 统一错误码定义
 */

export enum ErrorCode {
  // 系统级错误 (1000-1999)
  SYSTEM_ERROR = 1000,
  INTERNAL_SERVER_ERROR = 1001,
  SERVICE_UNAVAILABLE = 1002,
  TIMEOUT_ERROR = 1003,
  RATE_LIMIT_EXCEEDED = 1004,
  MAINTENANCE_MODE = 1005,

  // 认证授权错误 (2000-2999)
  UNAUTHORIZED = 2000,
  INVALID_TOKEN = 2001,
  TOKEN_EXPIRED = 2002,
  INVALID_CREDENTIALS = 2003,
  ACCOUNT_LOCKED = 2004,
  ACCOUNT_DISABLED = 2005,
  INSUFFICIENT_PERMISSIONS = 2006,
  ROLE_NOT_FOUND = 2007,
  PERMISSION_DENIED = 2008,

  // 请求验证错误 (3000-3999)
  VALIDATION_ERROR = 3000,
  INVALID_PARAMETER = 3001,
  MISSING_PARAMETER = 3002,
  INVALID_FORMAT = 3003,
  PARAMETER_OUT_OF_RANGE = 3004,
  INVALID_JSON = 3005,
  INVALID_FILE_FORMAT = 3006,
  FILE_TOO_LARGE = 3007,
  INVALID_URL = 3008,
  INVALID_EMAIL = 3009,
  INVALID_PHONE = 3010,

  // 业务逻辑错误 (4000-4999)
  BUSINESS_ERROR = 4000,
  ENTITY_NOT_FOUND = 4001,
  ENTITY_ALREADY_EXISTS = 4002,
  ENTITY_IN_USE = 4003,
  OPERATION_NOT_ALLOWED = 4004,
  STATUS_INVALID = 4005,
  QUOTA_EXCEEDED = 4006,
  DUPLICATE_OPERATION = 4007,
  PRECONDITION_FAILED = 4008,
  CONFLICT_ERROR = 4009,
  EXPIRED_ERROR = 4010,

  // 外部服务错误 (5000-5999)
  EXTERNAL_SERVICE_ERROR = 5000,
  COZE_API_ERROR = 5001,
  DOUYIN_API_ERROR = 5002,
  QINIU_API_ERROR = 5003,
  REDIS_CONNECTION_ERROR = 5004,
  DATABASE_CONNECTION_ERROR = 5005,
  WORKFLOW_EXECUTION_ERROR = 5006,
  AI_SERVICE_ERROR = 5007,
  THIRD_PARTY_API_ERROR = 5008,

  // 商户相关错误 (6000-6099)
  MERCHANT_NOT_FOUND = 6000,
  MERCHANT_ALREADY_EXISTS = 6001,
  MERCHANT_DISABLED = 6002,
  MERCHANT_QUOTA_EXCEEDED = 6003,
  MERCHANT_PERMISSION_DENIED = 6004,
  INVALID_MERCHANT_STATUS = 6005,

  // 项目相关错误 (6100-6199)
  PROJECT_NOT_FOUND = 6100,
  PROJECT_ALREADY_EXISTS = 6101,
  PROJECT_EXPIRED = 6102,
  PROJECT_SUSPENDED = 6103,
  PROJECT_LIMIT_EXCEEDED = 6104,
  INVALID_PROJECT_TYPE = 6105,
  PROJECT_ACCESS_DENIED = 6106,

  // IP账号相关错误 (6200-6299)
  IP_ACCOUNT_NOT_FOUND = 6200,
  IP_ACCOUNT_ALREADY_EXISTS = 6201,
  IP_ACCOUNT_INVALID_URL = 6202,
  IP_ACCOUNT_FETCH_FAILED = 6203,
  IP_ACCOUNT_ANALYSIS_FAILED = 6204,
  IP_ACCOUNT_QUOTA_EXCEEDED = 6205,

  // 视频相关错误 (6300-6399)
  VIDEO_NOT_FOUND = 6300,
  VIDEO_GENERATION_FAILED = 6301,
  VIDEO_PROCESSING_ERROR = 6302,
  INVALID_VIDEO_FORMAT = 6303,
  VIDEO_UPLOAD_FAILED = 6304,
  VIDEO_QUOTA_EXCEEDED = 6305,

  // 工作流相关错误 (6400-6499)
  WORKFLOW_NOT_FOUND = 6400,
  WORKFLOW_EXECUTION_FAILED = 6401,
  WORKFLOW_TIMEOUT = 6402,
  INVALID_WORKFLOW_PARAMS = 6403,
  WORKFLOW_QUOTA_EXCEEDED = 6404,
  WORKFLOW_PERMISSION_DENIED = 6405,

  // 文件相关错误 (6500-6599)
  FILE_NOT_FOUND = 6500,
  FILE_UPLOAD_FAILED = 6501,
  FILE_DOWNLOAD_FAILED = 6502,
  UNSUPPORTED_FILE_TYPE = 6503,
  FILE_SIZE_EXCEEDED = 6504,
  STORAGE_QUOTA_EXCEEDED = 6505,
}

export const ErrorMessages = {
  // 系统级错误
  [ErrorCode.SYSTEM_ERROR]: '系统错误',
  [ErrorCode.INTERNAL_SERVER_ERROR]: '内部服务器错误',
  [ErrorCode.SERVICE_UNAVAILABLE]: '服务不可用',
  [ErrorCode.TIMEOUT_ERROR]: '请求超时',
  [ErrorCode.RATE_LIMIT_EXCEEDED]: '请求频率超限',
  [ErrorCode.MAINTENANCE_MODE]: '系统维护中',

  // 认证授权错误
  [ErrorCode.UNAUTHORIZED]: '未授权访问',
  [ErrorCode.INVALID_TOKEN]: '无效的访问令牌',
  [ErrorCode.TOKEN_EXPIRED]: '访问令牌已过期',
  [ErrorCode.INVALID_CREDENTIALS]: '用户名或密码错误',
  [ErrorCode.ACCOUNT_LOCKED]: '账户已被锁定',
  [ErrorCode.ACCOUNT_DISABLED]: '账户已被禁用',
  [ErrorCode.INSUFFICIENT_PERMISSIONS]: '权限不足',
  [ErrorCode.ROLE_NOT_FOUND]: '角色不存在',
  [ErrorCode.PERMISSION_DENIED]: '访问被拒绝',

  // 请求验证错误
  [ErrorCode.VALIDATION_ERROR]: '请求参数验证失败',
  [ErrorCode.INVALID_PARAMETER]: '无效的参数',
  [ErrorCode.MISSING_PARAMETER]: '缺少必需参数',
  [ErrorCode.INVALID_FORMAT]: '参数格式错误',
  [ErrorCode.PARAMETER_OUT_OF_RANGE]: '参数超出范围',
  [ErrorCode.INVALID_JSON]: '无效的JSON格式',
  [ErrorCode.INVALID_FILE_FORMAT]: '不支持的文件格式',
  [ErrorCode.FILE_TOO_LARGE]: '文件大小超出限制',
  [ErrorCode.INVALID_URL]: '无效的URL格式',
  [ErrorCode.INVALID_EMAIL]: '无效的邮箱格式',
  [ErrorCode.INVALID_PHONE]: '无效的手机号格式',

  // 业务逻辑错误
  [ErrorCode.BUSINESS_ERROR]: '业务逻辑错误',
  [ErrorCode.ENTITY_NOT_FOUND]: '资源不存在',
  [ErrorCode.ENTITY_ALREADY_EXISTS]: '资源已存在',
  [ErrorCode.ENTITY_IN_USE]: '资源正在使用中',
  [ErrorCode.OPERATION_NOT_ALLOWED]: '操作不允许',
  [ErrorCode.STATUS_INVALID]: '状态无效',
  [ErrorCode.QUOTA_EXCEEDED]: '配额已超限',
  [ErrorCode.DUPLICATE_OPERATION]: '重复操作',
  [ErrorCode.PRECONDITION_FAILED]: '前置条件不满足',
  [ErrorCode.CONFLICT_ERROR]: '数据冲突',
  [ErrorCode.EXPIRED_ERROR]: '资源已过期',

  // 外部服务错误
  [ErrorCode.EXTERNAL_SERVICE_ERROR]: '外部服务错误',
  [ErrorCode.COZE_API_ERROR]: 'Coze API错误',
  [ErrorCode.DOUYIN_API_ERROR]: '抖音API错误',
  [ErrorCode.QINIU_API_ERROR]: '七牛云API错误',
  [ErrorCode.REDIS_CONNECTION_ERROR]: 'Redis连接错误',
  [ErrorCode.DATABASE_CONNECTION_ERROR]: '数据库连接错误',
  [ErrorCode.WORKFLOW_EXECUTION_ERROR]: '工作流执行错误',
  [ErrorCode.AI_SERVICE_ERROR]: 'AI服务错误',
  [ErrorCode.THIRD_PARTY_API_ERROR]: '第三方API错误',

  // 商户相关错误
  [ErrorCode.MERCHANT_NOT_FOUND]: '商户不存在',
  [ErrorCode.MERCHANT_ALREADY_EXISTS]: '商户已存在',
  [ErrorCode.MERCHANT_DISABLED]: '商户已被禁用',
  [ErrorCode.MERCHANT_QUOTA_EXCEEDED]: '商户配额已超限',
  [ErrorCode.MERCHANT_PERMISSION_DENIED]: '商户访问被拒绝',
  [ErrorCode.INVALID_MERCHANT_STATUS]: '商户状态无效',

  // 项目相关错误
  [ErrorCode.PROJECT_NOT_FOUND]: '项目不存在',
  [ErrorCode.PROJECT_ALREADY_EXISTS]: '项目已存在',
  [ErrorCode.PROJECT_EXPIRED]: '项目已过期',
  [ErrorCode.PROJECT_SUSPENDED]: '项目已暂停',
  [ErrorCode.PROJECT_LIMIT_EXCEEDED]: '项目数量超限',
  [ErrorCode.INVALID_PROJECT_TYPE]: '项目类型无效',
  [ErrorCode.PROJECT_ACCESS_DENIED]: '项目访问被拒绝',

  // IP账号相关错误
  [ErrorCode.IP_ACCOUNT_NOT_FOUND]: 'IP账号不存在',
  [ErrorCode.IP_ACCOUNT_ALREADY_EXISTS]: 'IP账号已存在',
  [ErrorCode.IP_ACCOUNT_INVALID_URL]: 'IP账号URL格式错误',
  [ErrorCode.IP_ACCOUNT_FETCH_FAILED]: 'IP账号数据获取失败',
  [ErrorCode.IP_ACCOUNT_ANALYSIS_FAILED]: 'IP账号分析失败',
  [ErrorCode.IP_ACCOUNT_QUOTA_EXCEEDED]: 'IP账号配额已超限',

  // 视频相关错误
  [ErrorCode.VIDEO_NOT_FOUND]: '视频不存在',
  [ErrorCode.VIDEO_GENERATION_FAILED]: '视频生成失败',
  [ErrorCode.VIDEO_PROCESSING_ERROR]: '视频处理错误',
  [ErrorCode.INVALID_VIDEO_FORMAT]: '视频格式不支持',
  [ErrorCode.VIDEO_UPLOAD_FAILED]: '视频上传失败',
  [ErrorCode.VIDEO_QUOTA_EXCEEDED]: '视频配额已超限',

  // 工作流相关错误
  [ErrorCode.WORKFLOW_NOT_FOUND]: '工作流不存在',
  [ErrorCode.WORKFLOW_EXECUTION_FAILED]: '工作流执行失败',
  [ErrorCode.WORKFLOW_TIMEOUT]: '工作流执行超时',
  [ErrorCode.INVALID_WORKFLOW_PARAMS]: '工作流参数无效',
  [ErrorCode.WORKFLOW_QUOTA_EXCEEDED]: '工作流配额已超限',
  [ErrorCode.WORKFLOW_PERMISSION_DENIED]: '工作流访问被拒绝',

  // 文件相关错误
  [ErrorCode.FILE_NOT_FOUND]: '文件不存在',
  [ErrorCode.FILE_UPLOAD_FAILED]: '文件上传失败',
  [ErrorCode.FILE_DOWNLOAD_FAILED]: '文件下载失败',
  [ErrorCode.UNSUPPORTED_FILE_TYPE]: '文件类型不支持',
  [ErrorCode.FILE_SIZE_EXCEEDED]: '文件大小超限',
  [ErrorCode.STORAGE_QUOTA_EXCEEDED]: '存储配额已超限',
};

export function getErrorMessage(code: ErrorCode): string {
  return ErrorMessages[code] || '未知错误';
}