import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { BusinessException } from '../exceptions/business.exception';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let code = 'INTERNAL_ERROR';
    let details: any = null;

    // 处理业务异常
    if (exception instanceof BusinessException) {
      const exceptionResponse = exception.getResponse() as any;
      status = exception.getStatus();
      message = exceptionResponse.message || exception.message;
      code = exceptionResponse.code || 'BUSINESS_ERROR';
    }
    // 处理HTTP异常
    else if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'object') {
        message = (exceptionResponse as any).message || exception.message;
        code = (exceptionResponse as any).code || 'HTTP_ERROR';
        details = (exceptionResponse as any).details;
      } else {
        message = exceptionResponse;
      }
    }
    // 处理其他异常
    else if (exception instanceof Error) {
      message = exception.message;
      code = 'RUNTIME_ERROR';
      
      // 记录详细错误信息
      this.logger.error(
        `Unhandled exception: ${exception.message}`,
        exception.stack
      );
    }

    // 构建错误响应
    const errorResponse = {
      success: false,
      code,
      message,
      data: null,
      ...(details && { details }),
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
    };

    // 记录错误日志
    this.logger.error(
      `${request.method} ${request.url} - ${status} - ${message}`,
      exception instanceof Error ? exception.stack : JSON.stringify(exception)
    );

    response.status(status).json(errorResponse);
  }
}