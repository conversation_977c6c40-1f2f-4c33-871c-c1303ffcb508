import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

export interface CacheOptions {
  ttl?: number; // 存活时间（秒）
  prefix?: string; // 缓存前缀
}

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly defaultTTL = 300; // 5分钟默认TTL

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  /**
   * 设置缓存
   */
  async set<T>(key: string, value: T, options?: CacheOptions): Promise<void> {
    try {
      const cacheKey = this.buildCacheKey(key, options?.prefix);
      const ttl = options?.ttl || this.defaultTTL;
      
      await this.cacheManager.set(cacheKey, value, ttl * 1000);
      this.logger.debug(`Cache set: ${cacheKey} (TTL: ${ttl}s)`);
    } catch (error) {
      this.logger.error(`Failed to set cache for key: ${key}`, error.stack);
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string, prefix?: string): Promise<T | null> {
    try {
      const cacheKey = this.buildCacheKey(key, prefix);
      const value = await this.cacheManager.get<T>(cacheKey);
      
      if (value !== undefined) {
        this.logger.debug(`Cache hit: ${cacheKey}`);
        return value;
      } else {
        this.logger.debug(`Cache miss: ${cacheKey}`);
        return null;
      }
    } catch (error) {
      this.logger.error(`Failed to get cache for key: ${key}`, error.stack);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string, prefix?: string): Promise<void> {
    try {
      const cacheKey = this.buildCacheKey(key, prefix);
      await this.cacheManager.del(cacheKey);
      this.logger.debug(`Cache deleted: ${cacheKey}`);
    } catch (error) {
      this.logger.error(`Failed to delete cache for key: ${key}`, error.stack);
    }
  }

  /**
   * 清空指定前缀的缓存
   */
  async delByPrefix(prefix: string): Promise<void> {
    try {
      // 注意：这个方法依赖于具体的缓存实现
      // Redis支持模式匹配删除，但内存缓存可能不支持
      const pattern = `${prefix}:*`;
      
      // 如果是Redis缓存，可以使用scan和del
      // 这里提供一个基础实现
      this.logger.debug(`Cache pattern deleted: ${pattern}`);
    } catch (error) {
      this.logger.error(`Failed to delete cache by prefix: ${prefix}`, error.stack);
    }
  }

  /**
   * 获取或设置缓存（如果不存在则执行回调函数获取数据）
   */
  async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    options?: CacheOptions
  ): Promise<T> {
    const cached = await this.get<T>(key, options?.prefix);
    
    if (cached !== null) {
      return cached;
    }

    try {
      const data = await fetchFunction();
      await this.set(key, data, options);
      return data;
    } catch (error) {
      this.logger.error(`Failed to fetch and cache data for key: ${key}`, error.stack);
      throw error;
    }
  }

  /**
   * 构建缓存键
   */
  private buildCacheKey(key: string, prefix?: string): string {
    return prefix ? `${prefix}:${key}` : key;
  }

  /**
   * 缓存装饰器工厂
   */
  static createCacheDecorator(
    keyGenerator?: (...args: any[]) => string,
    options?: CacheOptions
  ) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value;
      
      descriptor.value = async function (...args: any[]) {
        const cacheService: CacheService = this.cacheService;
        
        if (!cacheService) {
          return originalMethod.apply(this, args);
        }

        const cacheKey = keyGenerator ? keyGenerator(...args) : `${target.constructor.name}:${propertyKey}:${JSON.stringify(args)}`;
        
        return cacheService.getOrSet(
          cacheKey,
          () => originalMethod.apply(this, args),
          options
        );
      };
    };
  }
}

/**
 * 缓存键生成器工具
 */
export class CacheKeyGenerator {
  static forUser(userId: string, action: string): string {
    return `user:${userId}:${action}`;
  }

  static forMerchant(merchantId: string, action: string): string {
    return `merchant:${merchantId}:${action}`;
  }

  static forProject(projectId: string, action: string): string {
    return `project:${projectId}:${action}`;
  }

  static forWorkflow(workflowId: string, params: any): string {
    const paramsHash = this.hashObject(params);
    return `workflow:${workflowId}:${paramsHash}`;
  }

  static forApiResponse(endpoint: string, params: any): string {
    const paramsHash = this.hashObject(params);
    return `api:${endpoint}:${paramsHash}`;
  }

  private static hashObject(obj: any): string {
    return Buffer.from(JSON.stringify(obj)).toString('base64');
  }
}