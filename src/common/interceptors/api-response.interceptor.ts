import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable, map } from 'rxjs';
import { Response } from 'express';

export interface StandardApiResponse<T = any> {
  success: boolean;
  code: number;
  message: string;
  data: T;
  timestamp: string;
  path?: string;
  method?: string;
  version?: string;
}

@Injectable()
export class ApiResponseInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();
    
    return next.handle().pipe(
      map((data) => {
        // 如果数据已经是标准格式，直接返回
        if (this.isStandardResponse(data)) {
          return data;
        }

        // 构建标准响应格式
        const standardResponse: StandardApiResponse = {
          success: true,
          code: response.statusCode || 200,
          message: this.getSuccessMessage(response.statusCode),
          data: data,
          timestamp: new Date().toISOString(),
          path: request.url,
          method: request.method,
          version: '1.0',
        };

        return standardResponse;
      })
    );
  }

  private isStandardResponse(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      'success' in data &&
      'code' in data &&
      'message' in data &&
      'data' in data
    );
  }

  private getSuccessMessage(statusCode: number): string {
    const messages = {
      200: '操作成功',
      201: '创建成功',
      202: '请求已接受',
      204: '删除成功',
    };

    return messages[statusCode] || '操作成功';
  }
}