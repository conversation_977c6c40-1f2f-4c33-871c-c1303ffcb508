import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontex<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable, tap, catchError, throwError } from 'rxjs';
import { Request, Response } from 'express';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    
    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';
    const userId = (request as any).user?.id || 'anonymous';
    
    const startTime = Date.now();
    
    // 记录请求开始
    this.logger.log(
      `[${method}] ${url} - ${ip} - ${userAgent} - User: ${userId} - Start`
    );

    return next.handle().pipe(
      tap((data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        const { statusCode } = response;
        
        // 记录成功响应
        this.logger.log(
          `[${method}] ${url} - ${ip} - ${statusCode} - ${duration}ms - User: ${userId} - Success`
        );
        
        // 记录慢查询
        if (duration > 2000) {
          this.logger.warn(
            `[SLOW REQUEST] [${method}] ${url} - ${duration}ms - User: ${userId}`
          );
        }
      }),
      catchError((error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        const statusCode = error.status || 500;
        
        // 记录错误响应
        this.logger.error(
          `[${method}] ${url} - ${ip} - ${statusCode} - ${duration}ms - User: ${userId} - Error: ${error.message}`,
          error.stack
        );
        
        return throwError(error);
      })
    );
  }
}