import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { ConfigService } from '@nestjs/config';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { SKIP_AUTH_KEY } from '../decorators/skip-auth.decorator';
import { Reflector } from '@nestjs/core';
import { getAuthToken } from '../utils/common';
import { reqUser } from '../utils/nameSpace';
import { InjectRepository } from '@nestjs/typeorm';
import { Project } from 'src/modules/coze/project/entities/project.entity';
import { Repository } from 'typeorm';
import { User } from '../modules/user/entities/user.entity';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private configService: ConfigService, // 注入配置服务，用于获取配置
    @Inject(CACHE_MANAGER) private cache: Cache, // 注入缓存管理器���用于处理缓存
    private reflector: Reflector, // 注入 Reflector 服务
    @InjectRepository(Project) // 注入项目仓库
    private projectRepository: Repository<Project>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const skipAuth = this.reflector.getAllAndOverride<boolean>(SKIP_AUTH_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (skipAuth) {
      return true; // 如果存在 SkipAuth 装饰器，跳过验证
    }

    const http = context.switchToHttp(); // 获取HTTP上下文
    const request = http.getRequest<Request>(); // 获取请求对象

    const userInfo = await this.getUser(request);
    const projects = await this.getProjectInfo(userInfo);

    // 将用户信息和关联的项目信息都挂载到请求对象上
    request[reqUser] = {
      ...userInfo,
      projects,
    };

    return true; // 返回true，允许请求通过
  }

  public async getUser(request: Request) {
    const user = await this.extractTokenFromHeader(request); // 从请求头中提取token

    if (!user) {
      throw new UnauthorizedException('请先登陆'); // 如果没有token，抛出未授权异常
    }

    try {
      // 验证JWT token
      return user;
    } catch {
      throw new UnauthorizedException('请先登陆'); // 如果验证失败，抛出未授权异常
    }
  }

  private async extractTokenFromHeader(request: Request): Promise<User> {
    const key = await getAuthToken(request, this.cache);
    if (key) {
      return await this.cache.get(key); // 返回用户信息
    }
  }

  private async getProjectInfo(userInfo: any): Promise<any[]> {
    try {
      if (!userInfo || !userInfo.id) {
        throw new UnauthorizedException('用户信息无效');
      }

      // 查询该用户创建的所有项目
      return await this.projectRepository.find({
        where: {
          createdBy: userInfo.id,
        },
        order: {
          createdAt: 'DESC',
        },
      });
    } catch (error) {
      throw new UnauthorizedException('获取商户信息失败：' + error.message);
    }
  }
}
