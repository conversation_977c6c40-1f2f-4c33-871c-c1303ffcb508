import { NestFactory, Reflector } from '@nestjs/core';
import { AppModule } from './modules/app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { knife4jSetup } from 'nest-knife4j';
import { ClassSerializerInterceptor, ValidationPipe } from '@nestjs/common';
import { AuthGuard } from './guard/authGuard';
import { AllExceptionsFilter } from './filter/any-exception.filter';
import { LoggerService } from './logger/logger.service';
import config from './config';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';

// 自定义Swagger响应码
const CUSTOM_SUCCESS_RESPONSE = {
  description: '请求成功',
  schema: {
    type: 'object',
    properties: {
      code: {
        type: 'number',
        example: 0,
      },
      data: {
        type: 'object',
        example: {},
      },
      message: {
        type: 'string',
        example: '操作成功',
      },
    },
  },
};

const CUSTOM_ERROR_RESPONSE = {
  description: '请求失败',
  schema: {
    type: 'object',
    properties: {
      code: {
        type: 'number',
        example: 400,
      },
      message: {
        type: 'string',
        example: '参数验证失败',
      },
    },
  },
};

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  // app.use(bodyParser.json({ limit: "50mb" }));

  // 应用全局验证管道
  // 这行代码使用 NestJS 内置的 ValidationPipe 设置了一个全局验证管道。
  // ValidationPipe 会根据 DTO（数据传输对象）中定义的验证规则自动验证传入的请求。
  // 如果验证失败，它将抛出一个适当的错误响应。
  // 这有助于确保应用程序处理的数据是有效的，并且符合预期的格式和约束。
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: false,  // 不抛出未声明属性的错误
      skipMissingProperties: true,  // 跳过未定义的属性验证
    }),
  );
  // 启用序列化拦截器
  // 这行代码启用了 NestJS 的 ClassSerializerInterceptor。
  // ClassSerializerInterceptor 允许你控制哪些属性在序列化时返回给客户端。
  // 它可以通过在 DTO 类上使用 @Exclude() 装饰器来排除属性，或者使用 @Expose() 装饰器来指定哪些属性应该返回。
  // 这有助于确保只返回客户端需要的属性，而不是返回整个对象。
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));
  // 全局新增URL前缀
  app.setGlobalPrefix(config().prefix);

  // 配置静态文件服务
  app.useStaticAssets(join(__dirname, '..', 'public'), {
    index: 'api-doc-index.html', // 设置默认首页
    prefix: '/docs', // 路由前缀
  });

  // 启用CORS
  app.enableCors();

  // 全局响应配置
  const globalResponses = {
    200: CUSTOM_SUCCESS_RESPONSE,
    400: CUSTOM_ERROR_RESPONSE,
  };

  // src/main.ts 中修改Swagger配置部分

  // Swagger文档配置
  const options = new DocumentBuilder()
    .setTitle('API文档')
    .setDescription('所有接口文档，包含认证、抖音、用户等模块')
    .setVersion('1.0.0')
    .addBearerAuth()
    .addTag('auth', '认证模块')
    .addTag('user', '用户模块')
    .addTag('douyin', '抖音模块')
    .addTag('抖音账号', 'DY模块')
    .addTag('coze', 'COZE模块')
    .addTag('coze-ip', 'COZE-IP模块')
    .addTag('coze-project', 'COZE项目模块')
    .addTag('coze-benchmarking', 'COZE对标账号模块')
    .addTag('coze-material', 'COZE素材模块')
    .addTag('coze-human', 'COZE人设模块')
    .addTag('coze-hot', 'COZE热点模块')
    .addTag('coze-video', 'COZE视频模块')
    .addTag('coze-qa', 'COZE问答模块')
    .build();

  const document = SwaggerModule.createDocument(app, options);

  // 设置Swagger UI
  SwaggerModule.setup('api-docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      docExpansion: 'none',
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });

  // knife4j配置
  knife4jSetup(app, [
    {
      name: 'API文档',
      url: `/api-docs-json`,
      swaggerVersion: '3.0',
      location: `/api-docs-json`,
    },
  ]);

  app.useGlobalFilters(new AllExceptionsFilter());
  app.useLogger(app.get(LoggerService));

  app.useGlobalGuards(app.get(AuthGuard));
  await app.listen(3010);
}

bootstrap();
