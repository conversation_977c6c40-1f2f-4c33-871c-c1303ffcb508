import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MailerModule } from '@nestjs-modules/mailer';
import { ScheduleModule } from '@nestjs/schedule';
import { CozeModule } from './coze/coze.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ResponseInterceptor } from '../interceptor/response.interceptor';
import { LoggerModule } from 'src/logger/logger.module';
import { AuthModule } from './auth/auth.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from './user/user.module';
import { redisStore } from 'cache-manager-redis-yet';
import { CacheModule } from '@nestjs/cache-manager';
import { RedisClientOptions } from 'redis';
import configuration from '../config';
import { AuthGuard } from '../guard/authGuard';
import { DouyinModule } from './douyin/douyin.module';
import { Project } from './coze/project/entities/project.entity';
import { DyModule } from './dy/dy.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        transport: {
          isGlobal: true,
          host: configService.get('mail').host,
          port: configService.get('mail').port,
          auth: {
            user: configService.get('mail').auth.user,
            pass: configService.get('mail').auth.pass,
            secret: configService.get('mail').auth.secret,
          },
        },
        defaults: {
          from: '<EMAIL>',
        },
      }),
    }),
    CacheModule.registerAsync<RedisClientOptions>({
      imports: [ConfigModule],
      inject: [ConfigService],
      isGlobal: true,
      useFactory: async (configService: ConfigService) => {
        const store = await redisStore({
          socket: {
            host: configService.get('redis').host,
            port: +configService.get('redis').port,
          },
          password: configService.get('redis').password,
          ttl: 1000 * 60 * 60, // 1 hour
        });

        return { store };
      },
    }),
    UserModule,
    ConfigModule.forRoot({
      load: [configuration],
      isGlobal: true,
    }),
    TypeOrmModule.forRoot(configuration().database),
    LoggerModule,
    AuthModule,
    CozeModule,
    DouyinModule,
    DyModule,
          TypeOrmModule.forFeature([Project]),
  ],
  providers: [
    AuthGuard,
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
  ],
})
export class AppModule {}
