import * as md5 from 'md5';

interface IXBogus {
  getXB: (input: string) => string;
}

class XBogusGenerator implements IXBogus {
  private readonly BASE64_CHARS =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
  private readonly CUSTOM_DICT =
    'Dkdpgh4ZKsQB80/Mfvw36XI1R25-WUAlEi7NLboqYTOPuzmFjJnryx9HVGcaStCe=';
  private readonly LOOKUP_TABLE: (number | null)[] = Array(128)
    .fill(null)
    .map((_, i) => {
      if (i >= 48 && i <= 57) return i - 48; // 0-9
      if (i >= 97 && i <= 102) return i - 87; // a-f
      return null;
    });

  public getXB(input: string): string {
    console.log(6666, md5);
    const key1 = md5(
      'd4+pTKoNjJFb5tMtAC3XB9XrDDxlig1kjbh32u+x5YcwWb/me2pvLTh6ZdBVN5skEeIaOYNixbnFK6wyJdl/Lcy9CDAcpXLLQc3QFKIDQ3KkQYie3n258eLS1YFUqFLDjn7dqCRp1jjoORamU2SV',
    );
    const key2 = 'd41d8cd98f00b204e9800998ecf8427e';

    const n = this.hexToBytes(key1);
    const u = this.hexToBytes(md5(this.hexToBytes(key2)));
    const e = this.md5Bytes(input);

    const timestamp = Math.floor(Date.now() / 1000);
    const magic = 536919696;

    const data = [
      64,
      0.00390625,
      1,
      8,
      e[14],
      e[15],
      u[14],
      u[15],
      n[14],
      n[15],
      (timestamp >> 24) & 255,
      (timestamp >> 16) & 255,
      (timestamp >> 8) & 255,
      timestamp & 255,
      (magic >> 24) & 255,
      (magic >> 16) & 255,
      (magic >> 8) & 255,
      magic & 255,
    ];

    const checksum = data.reduce((a, b) => a ^ b);
    data.push(checksum);

    const first = data.filter((_, i) => i % 2 === 0);
    const second = data.filter((_, i) => i % 2 === 1);
    const combined = [...first, ...second].slice(0, 19);

    const encrypted = this.rc4(
      String.fromCharCode(255),
      String.fromCharCode(2, 255) +
        combined.map((x) => String.fromCharCode(x)).join(''),
    );

    let result = '';
    for (let i = 0; i < encrypted.length; i += 3) {
      result += this.encodeBlock(
        encrypted.charCodeAt(i),
        encrypted.charCodeAt(i + 1),
        encrypted.charCodeAt(i + 2),
      );
    }

    return result;
  }

  private hexToBytes(hex: string): number[] {
    const bytes: number[] = [];
    for (let i = 0; i < hex.length; i += 2) {
      bytes.push(
        ((this.LOOKUP_TABLE[hex.charCodeAt(i)] ?? 0) << 4) |
          (this.LOOKUP_TABLE[hex.charCodeAt(i + 1)] ?? 0),
      );
    }
    return bytes;
  }

  private md5Bytes(input: string): number[] {
    return this.hexToBytes(md5(this.hexToBytes(md5(input))));
  }

  private base64Index(char: string): number {
    return this.BASE64_CHARS.indexOf(char);
  }

  private base64Decode(input: string): string {
    let output = '';
    for (let i = 0; i < input.length - 3; i += 4) {
      const a = this.base64Index(input.charAt(i));
      const b = this.base64Index(input.charAt(i + 1));
      const c = this.base64Index(input.charAt(i + 2));
      const d = this.base64Index(input.charAt(i + 3));

      output += String.fromCharCode((a << 2) | (b >>> 4));
      if (input.charAt(i + 2) !== '=') {
        output += String.fromCharCode(((b << 4) & 240) | ((c >>> 2) & 15));
      }
      if (input.charAt(i + 3) !== '=') {
        output += String.fromCharCode(((c << 6) & 192) | d);
      }
    }
    return output;
  }

  private encodeBlock(a: number, b: number, c: number): string {
    const n = (a << 16) | (b << 8) | c;
    return (
      this.CUSTOM_DICT[(n >>> 18) & 63] +
      this.CUSTOM_DICT[(n >>> 12) & 63] +
      this.CUSTOM_DICT[(n >>> 6) & 63] +
      this.CUSTOM_DICT[n & 63]
    );
  }

  private rc4(key: string, data: string): string {
    const s = new Array(256);
    const len = key.length;
    let j = 0;
    let output = ''; // Initialize output as empty string

    // Initialize array
    for (let i = 0; i < 256; i++) {
      s[i] = i;
    }

    // Key scheduling
    for (let i = 0; i < 256; i++) {
      j = (j + s[i] + key.charCodeAt(i % len)) % 256;
      [s[i], s[j]] = [s[j], s[i]];
    }

    // Stream generation
    let i = 0;
    j = 0;
    for (let k = 0; k < data.length; k++) {
      i = (i + 1) % 256;
      j = (j + s[i]) % 256;
      [s[i], s[j]] = [s[j], s[i]];
      output += String.fromCharCode(
        data.charCodeAt(k) ^ s[(s[i] + s[j]) % 256],
      );
    }

    return output;
  }
}

export const xBogus = new XBogusGenerator();
export const getXB = xBogus.getXB.bind(xBogus);
