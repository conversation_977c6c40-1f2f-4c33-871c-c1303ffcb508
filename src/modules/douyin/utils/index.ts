import { stringify } from "qs";
import { tokenInfo } from "./config";
import { getXB } from "./X-Bogus";

let { odin_tt, passport_csrf_token, sessionid } = tokenInfo;
/**
 * 从 URL 中拆出 Sec_id
 * @param userUrl
 * @returns
 */
export const getTiktokSecId = (userUrl: string) => {
  const reg = /(?<=user\/)[^?]+/g;
  const result = userUrl.match(reg);
  if (result) return result[0];
  return null;
};

/**
 * 随机生成 107 位字符串
 * @param length
 * @returns
 */
export const generateRandomString = (length = 107) => {
  let result = "";
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

/**
 * 获取 Cookies
 * @param getTtwidFn
 * @returns
 */
export const getCookies = async (getTtwidFn: any) => {
  const ttwid = await getTtwidFn();
  const cookies = [
    `msToken=${generateRandomString()}`,
    ttwid,
    `odin_tt=${odin_tt}`,
    `passport_csrf_token=${passport_csrf_token}`,
    `sessionid=${sessionid}`
  ].join(";");

  return cookies;
};

/**
 * 拼接请求参数
 * @param sec_user_id
 * @param max_cursor
 * @returns
 */
export const transformParams = (sec_user_id: string, max_cursor: number) => {
  const params: any = {
    sec_user_id,
    count: 20,
    max_cursor,
    aid: 6383,
    cookie_enabled: true,
    platform: "PC",
    downlink: 10
  };

  console.log('Debug - params:', params);
  console.log('Debug - stringified params:', stringify(params));
  try {
    const xbValue = getXB(stringify(params));
    params["X-Bogus"] = xbValue;
  } catch (error) {
    console.error('Error in getXB:', error);
  }

  return stringify(params);
};

/**
 * 获取当前时间字符串
 * @returns {string} YYYYMMDDTHHmm 格式的时间字符串
 */
export const getDateTimeString = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, "0");
  const day = now.getDate().toString().padStart(2, "0");
  const hour = now.getHours().toString().padStart(2, "0");
  const minute = now.getMinutes().toString().padStart(2, "0");
  return `${year}${month}${day}T${hour}${minute}`;
};
