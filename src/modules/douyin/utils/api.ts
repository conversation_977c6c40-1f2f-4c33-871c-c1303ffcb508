import axios from 'axios';
import { ContentData, TiktokUserResponse } from '../type';
import { getCookies, getTiktokSecId, transformParams } from './index';
import { headerOption, likeBaseUrl, postBaseUrl, TTWideUrl } from './config';

let max_retry = 15;

/**
 * 基础请求封装
 * @param url 请求URL
 * @param option 请求选项
 * @returns 请求响应
 */
const request = async (url: string, option = {}) => {
  return axios(url, { headers: headerOption, ...option });
};

/**
 * 获取用户Sec_Id
 * @param userUrl 用户URL（短链或长链）
 * @returns 用户Sec_Id
 */
export const getUserSecId = async (userUrl: string): Promise<string> => {
  let userSecId: string | null = '';
  const urlRegex = /www\.iesdouyin\.com\/share\/user\//;

  if (urlRegex.test(userUrl)) {
    // 长链接直接使用
    userSecId = userUrl;
  } else {
    // 短链接需要获取重定向后的URL
    const response = await request(userUrl);

    userSecId = response.request.res.responseUrl; // axios中获取重定向URL
  }

  // 从URL中提取Sec_Id
  userSecId = getTiktokSecId(userSecId);

  if (!userSecId) throw new Error('Sec_Id 获取失败');
  return userSecId;
};

/**
 * 获取ttwid cookie
 * @returns ttwid cookie值
 */
const getTTWid = async (): Promise<string> => {
  // 构建请求体
  const postBody = {
    region: 'cn',
    aid: 1768,
    needFid: false,
    service: 'www.ixigua.com',
    migrate_info: { ticket: '', source: 'node' },
    cbUrlProtocol: 'https',
    union: true,
  };

  // 发送请求获取cookie
  const result = await axios.post(TTWideUrl, postBody);
  const ttwid = result.headers['set-cookie']?.[0];

  return ttwid ? ttwid.split(';')[0] : '';
};

/**
 * 生成获取用户内容的函数
 * @param type 内容类型：发布(post)或喜欢(like)
 * @returns 获取内容的函数
 */
export const getUserVideo = (type: string) => {
  // 根据类型选择请求URL
  let requestUrl = '';
  if (type === 'post') requestUrl = postBaseUrl;
  if (type === 'like') requestUrl = likeBaseUrl;

  /**
   * 获取用户内容数据
   * @param sec_uid 用户Sec_Id
   * @param max_cursor 分页游标
   * @returns 内容数据
   */
  return async (sec_uid: string, max_cursor: number): Promise<ContentData> => {
    // 准备请求参数
    let requestParams = transformParams(sec_uid, max_cursor);
    let cookie = await getCookies(getTTWid);
    let loopCount = 0;
    let responseJSON: TiktokUserResponse | null = null;

    // 重试机制
    while (loopCount <= max_retry && !responseJSON) {
      if (loopCount > 0)
        console.log(`第 ${loopCount}/${max_retry} 次重复请求...`);
      loopCount += 1;

      try {
        // 发送请求
        const response = await axios.get(requestUrl + requestParams, {
          headers: { ...headerOption, cookie },
        });
        console.log(678677, response);

        responseJSON = response.data;
      } catch (error) {
        // 每尝试 10 次等待 2s，并刷新参数
        if (loopCount % 10 === 0 && !responseJSON) {
          await new Promise((resolve) => setTimeout(resolve, 2000));
          requestParams = transformParams(sec_uid, max_cursor);
          cookie = await getCookies(getTTWid);
        }
      }
    }

    // 处理请求失败情况
    if (!responseJSON) {
      console.log('超出最大请求次数，停止请求...');
      return { list: [], max_cursor: 0, has_more: false };
    }

    // 返回处理后的数据
    return {
      list: responseJSON.aweme_list,
      max_cursor: responseJSON.max_cursor,
      has_more: responseJSON.has_more,
    };
  };
};
