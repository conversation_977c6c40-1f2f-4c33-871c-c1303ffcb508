import { Injectable, Logger } from '@nestjs/common';
import { getUserSecId, getUserVideo } from '../utils/api';
import { ContentData, TiktokContentItem } from '../type';
import { DouyinFindResDto } from '../dto/res/douyin.find.res.dto';
import { Benchmarking } from '../../coze/benchmarking/entities/benchmarking.entity';
import { runWorkflow } from '../../../utils/common';

/**
 * 获取用户内容数据
 * @param user 用户URL
 * @param type 类型：喜欢(like)或发布(post)
 * @param limit 数量限制，0表示无限制
 * @returns 内容数据
 */
export const fetchUserContent = async (
  user: string,
  type: string,
  limit: number = 0,
): Promise<ContentData> => {
  // 获取用户SecId
  const userSecId = await getUserSecId(user);
  if (!userSecId) {
    throw new Error('获取用户ID失败');
  }

  // 初始化变量
  const contentList: TiktokContentItem[] = [];
  const fetchVideoFn = getUserVideo(type);
  const listMaxRetryLimit = 15; // 列表最大重试限制

  let hasMore = true; // 是否还有更多数据
  let maxCursor = 0; // 最大游标
  let pageCount = 0; // 当前页码
  let maxRetry = 1; // 当前重试次数

  /**
   * 尝试获取一页数据
   * @returns 获取状态
   */
  const fetchPage = async (): Promise<{
    success: boolean;
    shouldBreak: boolean;
  }> => {
    try {
      // 限制检查，超出限制中断循环
      if (limit !== 0 && limit <= contentList.length) {
        return { success: true, shouldBreak: true };
      }

      const { list, max_cursor, has_more } = await fetchVideoFn(
        userSecId,
        maxCursor,
      );

      // 处理空列表情况
      if (!list || list.length === 0) {
        if (maxRetry <= listMaxRetryLimit) {
          maxRetry++;
          pageCount--;
          return { success: false, shouldBreak: false };
        }
        return { success: true, shouldBreak: true };
      }

      // 重置重试计数器，更新分页参数
      maxRetry = 1;
      hasMore = !!has_more;
      maxCursor = max_cursor;

      // 添加列表数据
      contentList.push(...list);

      return { success: true, shouldBreak: false };
    } catch (error) {
      console.log(error);
      if (maxRetry <= listMaxRetryLimit) {
        maxRetry++;
        pageCount--;
        return { success: false, shouldBreak: false };
      }
      return { success: false, shouldBreak: true };
    }
  };

  // 循环获取所有分页数据
  while (hasMore) {
    const { success, shouldBreak } = await fetchPage();
    if (shouldBreak) break;
    if (!success) {
      pageCount++;
      if (pageCount >= listMaxRetryLimit) {
        throw new Error(`获取数据失败，已重试${listMaxRetryLimit}次`);
      }
      // 等待一段时间后重试
      await new Promise((resolve) => setTimeout(resolve, 1000));
      continue;
    }
    pageCount++;
  }

  // 如果有限制，确保不超过限制
  const finalList = limit > 0 ? contentList.slice(0, limit) : contentList;

  return {
    list: finalList,
    max_cursor: maxCursor,
    has_more: hasMore,
  };
};

@Injectable()
export class DouyinService {
  private readonly logger = new Logger(DouyinService.name);

  async getUpInfoServer(req) {
    const { user, type = 'post', limit = 100 } = req;

    console.log(788, req, type, limit);
    const response = await fetchUserContent(user, type, limit);

    // 验证响应数据结构
    if (!response.list || !Array.isArray(response.list)) {
      throw new Error('无效的响应数据结构');
    }

    // 格式化返回数据
    const result: DouyinFindResDto[] = response.list.map(
      (item: TiktokContentItem) => ({
        awemeId: item.aweme_id,
        description: item.desc,
        videoUrl: item.video?.play_addr?.url_list?.[0] || item.url,
        audioUrl: item.music?.play_url?.url_list?.[0] || item.audioUrl,
        upName: item.author.nickname,
        type: item.aweme_type === 2 ? 'image' : 'video',
        diggCount: item.statistics?.digg_count || 0,
        commentCount: item.statistics?.comment_count || 0,
        collectCount: item.statistics?.collect_count || 0,
        shareCount: item.statistics?.share_count || 0,
      }),
    );
    console.log('抖音接口数据', result);
    return result;
  }

  /**
   * 刷新IP账号的视频数据
   * 从抖音获取数据、处理音频并返回Benchmarking实体
   * @param ipId IP账号ID
   * @param douyinUrl 抖音账号URL
   * @param limit 限制获取视频数量
   * @returns 处理后的Benchmarking实体数组及统计信息
   */
  async refreshBenchmarkingVideos(
    ipId: string,
    douyinUrl: string,
    limit: number = 20,
  ): Promise<{
    entities: Benchmarking[];
    totalCount: number;
    processedCount: number;
  }> {
    try {
      this.logger.log(`开始从抖音URL获取视频数据: ${douyinUrl}，限制数量: ${limit}`);

      // 从抖音获取数据
      const douyinData = await this.getUpInfoServer({
        user: douyinUrl,
        type: 'post',
        limit,
      });

      this.logger.log(`从抖音获取到 ${douyinData.length} 条视频数据`);

      // 过滤有音频的数据
      const filteredData = douyinData.filter(data => data.audioUrl);
      
      if (filteredData && filteredData.length > 0) {
        this.logger.log(`有 ${filteredData.length} 条视频包含音频，准备处理`);
        
        // 调用语音识别工作流处理音频
        const audioResults = await runWorkflow('7511265935560441891', {
          input: filteredData.map(data => ({
            audio_url: data.audioUrl,
          })),
        });

        // 创建Benchmarking实体数组
        const benchmarkingEntities: Benchmarking[] = [];

        for (const item of filteredData) {
          const audioInfo = audioResults.find(
            (data: any) => data.audio_url === item.audioUrl,
          );
          
          // 只处理有足够长文本的音频
          if (audioInfo && audioInfo.text && audioInfo.text.length > 50) {
            const benchmarking = new Benchmarking();
            benchmarking.awemeId = item.awemeId;
            benchmarking.description = item.description;
            benchmarking.videoUrl = item.videoUrl;
            benchmarking.audioUrl = item.audioUrl;
            benchmarking.upName = item.upName;
            benchmarking.type = item.type;
            benchmarking.audioText = audioInfo.text;
            benchmarking.ipId = ipId;
            benchmarkingEntities.push(benchmarking);
          }
        }

        this.logger.log(`成功处理 ${benchmarkingEntities.length} 条视频数据`);
        
        return {
          entities: benchmarkingEntities,
          totalCount: filteredData.length,
          processedCount: benchmarkingEntities.length,
        };
      } else {
        this.logger.warn(`未找到包含音频的视频数据`);
        return {
          entities: [],
          totalCount: 0,
          processedCount: 0,
        };
      }
    } catch (error) {
      this.logger.error(`刷新视频数据失败: ${error.message}`, error.stack);
      throw new Error(`刷新视频数据失败: ${error.message}`);
    }
  }
}
