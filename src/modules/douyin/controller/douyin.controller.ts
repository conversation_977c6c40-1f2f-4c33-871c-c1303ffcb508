import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GetVideoReqDto } from '../dto/req/getVideo.req.dot';
import { DouyinFindResDto } from '../dto/res/douyin.find.res.dto';
import { DouyinService } from '../services/douyin.service';

@ApiTags('douyin')
@Controller('douyin')
export class DouyinController {
  constructor(private readonly appService: DouyinService) {}

  @Post('getUpInfo')
  @ApiOperation({
    summary: '获取抖音UP主信息和视频列表',
    description: '根据抖音用户链接或ID获取用户发布或喜欢的视频列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: [DouyinFindResDto],
  })
  @ApiResponse({
    status: 400,
    description: '参数错误',
    schema: {
      example: { 
        code: 400, 
        msg: '缺少必要参数', 
        data: null 
      }
    }
  })
  async getUpInfo(@Body() req: GetVideoReqDto) {
    // 从请求体中获取用户标识和限制数量,type为post或like,type为发布类型，默认为发布
    const { user } = req;

    console.log('getUpInfo', req);
    // 参数验证
    if (!user) {
      return { code: 400, msg: '缺少必要参数', data: null };
    }

    return await this.appService.getUpInfoServer(req);
  }
}
