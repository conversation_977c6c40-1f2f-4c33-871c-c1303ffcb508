/**
 * 抖音用户喜欢/发布列表接口返回数据结构
 */
export interface TiktokUserResponse {
  /** 状态码 */
  status_code: number;
  /** 内容列表 */
  aweme_list: TiktokContentItem[];
  /** 分页游标 */
  max_cursor: number;
  /** 是否有更多数据 */
  has_more: boolean;
}

/**
 * 抖音内容项
 */
export interface TiktokContentItem {
  /** 视频链接 */
  url?: string;
  /** 音频链接 */
  audioUrl?: string;
  /** 视频信息 */
  info?: Record<string, any>;
  /** 内容类型：普通视频(0)或图集(2) */
  aweme_type: 0 | 2;
  /** 内容唯一ID */
  aweme_id: string;
  /** 内容描述 */
  desc: string;
  /** 作者信息 */
  author: {
    /** 作者昵称 */
    nickname: string;
    /** 作者ID */
    uid?: string;
    /** 作者头像 */
    avatar?: string;
  };
  /** 视频信息 */
  video?: TiktokVideoInfo;
  /** 图集信息 */
  images?: TiktokImageInfo[];
  /** 音乐信息 */
  music?: TiktokMusicInfo;
  /** 统计信息 */
  statistics?: {
    /** 点赞数 */
    digg_count: number;
    /** 评论数 */
    comment_count: number;
    /** 收藏数 */
    collect_count: number;
    /** 分享数 */
    share_count: number;
  };
}

/**
 * 视频信息
 */
export interface TiktokVideoInfo {
  /** 播放地址信息 */
  play_addr?: {
    /** 播放地址列表 */
    url_list?: string[];
    /** 视频唯一标识 */
    uri?: string;
  };
  /** 不同码率的视频信息 */
  bit_rate?: {
    /** 播放地址信息 */
    play_addr?: {
      /** 播放地址列表 */
      url_list?: string[];
    };
  }[];
}

/**
 * 图片信息
 */
export interface TiktokImageInfo {
  /** 图片URL列表 */
  url_list: string[];
  /** 图片下载URL列表 */
  download_url_list: string[];
}

/**
 * 音乐信息
 */
export interface TiktokMusicInfo {
  /** 播放地址 */
  play_url?: {
    /** 音频唯一标识 */
    uri?: string;
    /** 音频URL列表 */
    url_list?: string[];
  };
}

/**
 * API返回的内容数据
 */
export interface ContentData {
  /** 内容列表 */
  list: TiktokContentItem[];
  /** 分页游标 */
  max_cursor: number;
  /** 是否有更多数据 */
  has_more: boolean;
}
