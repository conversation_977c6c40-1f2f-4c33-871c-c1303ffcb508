import { Column } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEnum } from 'class-validator';

export class Douyin {
  @ApiProperty({ example: '7345678901234567890', description: '抖音视频ID' })
  @IsNotEmpty()
  awemeId: string;

  @ApiProperty({ example: '这是一个抖音视频描述', description: '视频描述' })
  description: string;

  @ApiProperty({
    example: 'https://example.com/video.mp4',
    description: '视频URL',
  })
  videoUrl: string;

  @ApiProperty({
    example: 'https://example.com/audio.mp3',
    description: '音频URL',
  })
  audioUrl: string;

  @ApiProperty({ example: '用户昵称', description: '作者昵称' })
  @IsNotEmpty()
  upName: string;

  @ApiProperty({
    example: 'video',
    description: '内容类型',
    enum: ['video', 'image'],
  })
  @IsEnum(['video', 'image'])
  @IsString()
  type: string;
}
