import { ApiProperty } from '@nestjs/swagger';

export class DouyinFindResDto {
  @ApiProperty({ example: '7123456789', description: '抖音视频ID' })
  awemeId: string;

  @ApiProperty({ example: '这是一个视频描述', description: '视频描述' })
  description: string;

  @ApiProperty({
    example: 'https://example.com/video.mp4',
    description: '视频URL',
  })
  videoUrl: string;

  @ApiProperty({
    example: 'https://example.com/audio.mp3',
    description: '音频URL',
  })
  audioUrl: string;

  @ApiProperty({ example: '用户昵称', description: '作者昵称' })
  upName: string;

  @ApiProperty({
    example: 'video',
    description: '内容类型',
    enum: ['video', 'image'],
  })
  type: 'video' | 'image';

  @ApiProperty({ example: 1000, description: '点赞数' })
  diggCount: number;

  @ApiProperty({ example: 100, description: '评论数' })
  commentCount: number;

  @ApiProperty({ example: 50, description: '收藏数' })
  collectCount: number;

  @ApiProperty({ example: 30, description: '分享数' })
  shareCount: number;
}
