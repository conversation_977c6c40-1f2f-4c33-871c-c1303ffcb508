import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsIn,
} from 'class-validator';

export class GetVideoReqDto {
  @ApiProperty({
    description: '抖音用户链接或ID',
    example: 'https://www.douyin.com/user/MS4wLjABAAAAxxxxx',
  })
  @IsNotEmpty({ message: '用户链接不能为空' })
  @IsString()
  user: string;

  @ApiProperty({
    description: '获取数量限制(0表示无限制)',
    example: 100,
    default: 100,
  })
  @IsOptional()
  @IsNumber()
  limit?: number = 100;

  @ApiProperty({
    description: '内容类型',
    enum: ['post', 'like'],
    default: 'post',
  })
  @IsOptional()
  @IsString()
  @IsIn(['post', 'like'])
  type?: string = 'post';
}
