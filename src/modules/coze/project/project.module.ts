import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Project } from './entities/project.entity';
import { ProjectController } from './controller/project.controller';
import { ProjectService } from './services/project.service';
import { IpService } from '../ip/services/ip.service';
import { CozeWorkflowManager } from '../coze-workflow.manager';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Project,
    ]),
  ],
  controllers: [ProjectController],
  providers: [ProjectService, IpService, CozeWorkflowManager],
  exports: [ProjectService]
})
export class ProjectModule {}