import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { REQUEST } from '@nestjs/core';
import { ProjectService } from './services/project.service';
import { Project } from './entities/project.entity';
import { ProjectReqDto } from './dto/req/project.req.dto';
import { EntityNotFoundException } from '../../../common/exceptions/business.exception';

describe('ProjectService', () => {
  let service: ProjectService;
  let repository: Repository<Project>;
  let mockRequest: any;

  const mockProject: Project = {
    id: '1',
    name: 'Test Project',
    projectName: 'Test Project',
    knowledge: 'Test Knowledge',
    accountPositioning: 'Test Positioning',
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'user1',
    updatedBy: 'user1',
  } as Project;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    findAndCount: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    mockRequest = {
      user: {
        id: 'user1',
        username: 'testuser',
        projects: [{ id: '1', name: 'Test Project' }],
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProjectService,
        {
          provide: getRepositoryToken(Project),
          useValue: mockRepository,
        },
        {
          provide: REQUEST,
          useValue: mockRequest,
        },
      ],
    }).compile();

    service = module.get<ProjectService>(ProjectService);
    repository = module.get<Repository<Project>>(getRepositoryToken(Project));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createMerchant', () => {
    it('should create a project successfully', async () => {
      const createDto: CreateMerchantReqDto = {
        name: 'New Merchant',
        projectName: 'New Project',
        knowledge: 'New Knowledge',
        
      };

      mockRepository.create.mockReturnValue(mockMerchant);
      mockRepository.save.mockResolvedValue(mockMerchant);

      const result = await service.createMerchant(createDto);

      expect(mockRepository.create).toHaveBeenCalledWith({
        ...createDto,
        createdBy: 'user1',
      });
      expect(mockRepository.save).toHaveBeenCalledWith(mockMerchant);
      expect(result).toEqual(mockMerchant);
    });

    it('should throw error when creation fails', async () => {
      const createDto: CreateMerchantReqDto = {
        name: 'New Merchant',
        projectName: 'New Project',
        knowledge: 'New Knowledge',
        
      };

      mockRepository.create.mockReturnValue(mockMerchant);
      mockRepository.save.mockRejectedValue(new Error('Database error'));

      await expect(service.createMerchant(createDto)).rejects.toThrow('Database error');
    });
  });

  describe('findAllMerchant', () => {
    it('should return paginated projects', async () => {
      const filter = {
        pageIndex: 1,
        pageSize: 10,
      };

      mockRepository.findAndCount.mockResolvedValue([[mockMerchant], 1]);

      const result = await service.findAllMerchant(filter);

      expect(result.data).toEqual([mockMerchant]);
      expect(result.total).toBe(1);
    });

    it('should filter by user projects when authMode is project', async () => {
      const filter = {
        pageIndex: 1,
        pageSize: 10,
      };

      mockRepository.findAndCount.mockResolvedValue([[mockMerchant], 1]);

      await service.findAllMerchant(filter);

      expect(mockRepository.findAndCount).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            createdBy: 'user1',
          }),
        })
      );
    });
  });

  describe('findMerchant', () => {
    it('should return project by id', async () => {
      mockRepository.findOne.mockResolvedValue(mockMerchant);

      const result = await service.findMerchant('1');

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
      });
      expect(result).toEqual(mockMerchant);
    });

    it('should throw EntityNotFoundException when project not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findMerchant('nonexistent')).rejects.toThrow(EntityNotFoundException);
    });
  });

  describe('updateMerchant', () => {
    it('should update project successfully', async () => {
      const updateDto: UpdateMerchantReqDto = {
        name: 'Updated Merchant',
      };

      const updatedMerchant = { ...mockMerchant, ...updateDto };
      
      mockRepository.findOne.mockResolvedValue(mockMerchant);
      mockRepository.update.mockResolvedValue({ affected: 1 });
      mockRepository.findOne.mockResolvedValueOnce(updatedMerchant);

      const result = await service.updateMerchant('1', updateDto);

      expect(mockRepository.update).toHaveBeenCalledWith('1', {
        ...updateDto,
        updatedBy: 'user1',
      });
      expect(result).toEqual(updatedMerchant);
    });

    it('should throw EntityNotFoundException when updating non-existent project', async () => {
      const updateDto: UpdateMerchantReqDto = {
        name: 'Updated Merchant',
      };

      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.updateMerchant('nonexistent', updateDto)).rejects.toThrow(EntityNotFoundException);
    });
  });

  describe('deleteMerchant', () => {
    it('should delete project successfully', async () => {
      mockRepository.findOne.mockResolvedValue(mockMerchant);
      mockRepository.delete.mockResolvedValue({ affected: 1 });

      await service.deleteMerchant({ id: '1' });

      expect(mockRepository.delete).toHaveBeenCalledWith('1');
    });

    it('should throw EntityNotFoundException when deleting non-existent project', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.deleteMerchant({ id: 'nonexistent' })).rejects.toThrow(EntityNotFoundException);
    });
  });
});