import {
  Column,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToOne,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { ZtBaseEntity } from '../../../../utils/base.entity';
import { IpEntity } from '../../ip/entities/ip.entity';

/**
 * 项目实体类
 * 
 * 该实体用于存储和管理系统中的项目信息，包括基本信息、业务设置以及关联的IP账号。
 * 项目是系统的核心业务实体，代表一个客户或企业的具体项目，拥有自己的IP资源。
 * 
 * 实体与IpEntity（IP账号）存在多对多的关联关系，一个项目可以管理多个IP账号，一个IP账号也可以被多个项目使用。
 * 继承自ZtBaseEntity，包含基础字段如id、createdAt、updatedAt等。
 */
@Entity()
export class Project extends ZtBaseEntity {
  /**
   * 项目名称
   * 
   * 项目的官方名称，通常是企业、组织或个人的正式名称
   * 在系统中唯一标识一个项目的业务名称
   * 该字段必须提供且不能为空，是项目的主要识别信息
   */
  @ApiProperty({ example: '示例项目', description: '项目名称' })
  @Column()
  @IsNotEmpty()
  name: string;

  /**
   * 抖音账号名称
   * 
   * 项目正在运营的抖音账号名称
   * 一个项目对应一个主要的抖音账号
   * 该字段必须提供且不能为空，用于界面展示和账号管理
   */
  @ApiProperty({ example: '示例账号', description: '抖音账号名称' })
  @Column()
  @IsNotEmpty()
  projectName: string;

  /**
   * 项目知识库
   * 
   * 存储与项目相关的详细知识、背景资料、运营指南等内容
   * 为项目的内容创作和运营提供基础知识支持
   * 支持最多100,000个字符，类型为text以支持大量文本
   * 该字段必须提供且不能为空
   */
  @ApiProperty({
    example: '项目相关知识',
    description: '项目知识库',
    maxLength: 100000,
  })
  @Column('text')
  @IsNotEmpty()
  knowledge: string;

  /**
   * 账号定位
   * 
   * 描述项目账号的市场定位和目标受众
   * 指导内容创作和运营策略的方向
   * 该字段可以为空，类型为text以支持长文本
   */
  @ApiProperty({ example: '企业账号', description: '账号定位' })
  @Column('text', { nullable: true })
  accountPositioning: string;

  /**
   * 当前选中的对标账号ID
   * 
   * 存储项目当前正在使用的对标账号(IP)的ID
   * 用于在多个关联IP中标识当前活跃使用的账号
   * 该字段可以为空，表示尚未选择对标账号
   */
  @ApiProperty({ example: '1', description: '当前选中的对标账号IP的id' })
  @Column({ nullable: true })
  selectedBenchmarkingAccountId: string;



  /**
   * 项目状态
   * 
   * 表示项目账号的状态，值为字符串类型：
   * '1' - 正常状态，允许使用系统功能
   * '0' - 禁用状态，限制系统功能使用
   * 默认值为'1'，表示新创建的项目默认为正常状态
   */
  @ApiProperty({ example: '1', description: '项目状态，1-正常，0-禁用' })
  @Column({ default: '1' })
  @IsNotEmpty()
  status: string;




  /**
   * IP账号关联
   * 
   * 与IpEntity实体的多对多关系，存储项目管理的所有IP账号
   * 使用@JoinTable()创建连接表实现多对多关系
   * 一个项目可以管理多个IP账号，一个IP账号也可以被多个项目使用
   */
  @ManyToMany(() => IpEntity, (ip) => ip.projects)
  @JoinTable()
  ips: IpEntity[];
}
