// src/modules/auth/dto/reset-password.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { Column } from 'typeorm';
import { ZtBaseReqDto } from '../../../../../utils/baseReq.dto';

export class ProjectReqDto extends ZtBaseReqDto {
  /**
   * 项目名称
   */
  @ApiProperty({ example: '示例项目', description: '项目名称' })
  @Column()
  @IsNotEmpty()
  name: string;

  /**
   * 抖音账号名称
   */
  @ApiProperty({ example: '示例账号', description: '抖音账号名称' })
  @Column()
  @IsNotEmpty()
  projectName: string;

  /**
   * 项目知识库，描述内容最大100000字符
   */
  @ApiProperty({
    example: '项目相关知识',
    description: '项目知识库',
    maxLength: 100000,
  })
  @Column('text')
  @IsNotEmpty()
  knowledge: string;

  /**
   * 对标账号名称
   */
  @ApiProperty({ example: '竞品账号A', description: '对标账号名称' })
  @Column()
  @IsNotEmpty()
  selectedBenchmarkingAccountId: string;




}
