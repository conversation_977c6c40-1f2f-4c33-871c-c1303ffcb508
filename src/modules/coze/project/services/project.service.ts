import { Injectable, Inject, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Project } from '../entities/project.entity';
import { ProjectResDto } from '../dto/res/project.res.dto';
import { ProjectReqDto } from '../dto/req/project.req.dto';
import { CommonService } from '../../../../utils/common.service';
import { RemoveReqDto } from 'src/utils/remove.req.dto';
import { ZtBaseResDto } from '../../../../utils/baseRes.dto';
import { CozeWorkflowManager } from '../../coze-workflow.manager';
import { IpService } from '../../ip/services/ip.service';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { reqUser } from '../../../../utils/nameSpace';

@Injectable()
export class ProjectService extends CommonService {
  private readonly logger = new Logger(ProjectService.name);

  constructor(
    @InjectRepository(Project)
    private readonly projectRepository: Repository<Project>,
    @Inject(REQUEST) request: Request,
    private ipService: IpService,
    private cozeWorkflowManager: CozeWorkflowManager,
  ) {
    super(request);
  }

  // Project CRUD
  async createProject(project: ProjectReqDto): Promise<void> {
    return this.createEntity(this.projectRepository, project, 'Project');
  }

  async updateProject(
    id: string,
    projectDto: Partial<ProjectReqDto>,
  ): Promise<void> {
    return this.updateEntity(
      this.projectRepository,
      id,
      projectDto,
      'Project',
    );
  }

  async deleteProject(params: RemoveReqDto): Promise<void> {
    return this.deleteEntity(this.projectRepository, params, 'Project');
  }

  async findProject(filter: Partial<Project>): Promise<ZtBaseResDto> {
    try {
      // 获取当前用户信息
      const currentUser = this.request[reqUser];
      const userId = currentUser?.id;

      // 添加createdBy过滤条件，确保只返回当前用户创建的项目
      const filterWithCreatedBy = {
        ...filter,
        createdBy: userId
      };

      // 查询全部IP
      const allIps = await this.ipService.findAll();
      const result = await this.findWithPagination(
        this.projectRepository,
        filterWithCreatedBy,
        ProjectResDto,
        { authMode: 'createdBy' }
      );

      // 处理数据，添加IP名称
      if (result.data?.length) {
        result.data = result.data.map((project) => {
          if (project.selectedBenchmarkingAccountId) {
            const matchedIp = allIps.find(
              (ip) => ip.id === project.selectedBenchmarkingAccountId,
            );
            return {
              ...project,
              selectedBenchmarkingAccountName:
                matchedIp?.benchmarkingAccount || '',
            };
          }
          return {
            ...project,
            selectedBenchmarkingAccountName: '',
          };
        });
      }

      return result;
    } catch (error) {
      this.logger.error(`查询项目失败: ${error.message}`, error.stack);
      throw new Error(`查询项目失败: ${error.message}`);
    }
  }

  async findOne(id: string): Promise<Project> {
    return this.projectRepository.findOne({ where: { id: id } });
  }

  async findAllProject(filter: Partial<Project>): Promise<Project[]> {
    try {
      // 获取当前用户信息
      const currentUser = this.request[reqUser];
      const userId = currentUser?.id;

      // 添加createdBy过滤条件，确保只返回当前用户创建的项目
      const filterWithCreatedBy = {
        ...filter,
        createdBy: userId
      };

      return this.findAllEntities(this.projectRepository, filterWithCreatedBy, { sortBy: 'createdAt', sortOrder: 'descend' }, { authMode: 'createdBy' });
    } catch (error) {
      this.logger.error(`查询所有项目失败: ${error.message}`, error.stack);
      throw new Error(`查询项目失败: ${error.message}`);
    }
  }

  async syncData(projectId: string, isEdit: boolean = false) {
    const project = await this.findOne(projectId);

    const obj: any = {};
    obj.project_id = project.id;
    obj.name = project.name;
    obj.project_name = project.projectName;
    obj.knowledge = project.knowledge;
    obj.account_positioning = project.accountPositioning;
    if (isEdit) {
      return await this.cozeWorkflowManager.editProjectData(obj);
    } else {
      return await this.cozeWorkflowManager.createProjectData(obj);
    }
  }

  async syncDataDelete(id: string) {
    return await this.cozeWorkflowManager.deleteProjectData(id);
  }
}
