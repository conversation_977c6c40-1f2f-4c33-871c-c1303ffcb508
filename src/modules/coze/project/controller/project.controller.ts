import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Project } from '../entities/project.entity';
import { RemoveReqDto } from '../../../../utils/remove.req.dto';
import { ProjectService } from '../services/project.service';
import { ProjectReqDto } from '../dto/req/project.req.dto';

@ApiTags('coze-project')
@Controller('coze/project')
export class ProjectController {
  constructor(private readonly projectService: ProjectService) {}

  // Project CRUD
  @Post('create')
  @ApiOperation({ summary: '创建项目' })
  @ApiResponse({ status: 201, description: '项目创建成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  async createProject(@Body() project: ProjectReqDto) {
    return await this.projectService.createProject(project);
  }

  @Post('update')
  @ApiOperation({ summary: '更新项目' })
  @ApiResponse({ status: 200, description: '项目更新成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  async updateProject(@Body() project: ProjectReqDto) {
    return await this.projectService.updateProject(
      project.id,
      project,
    );
  }

  @Post('remove')
  @ApiOperation({ summary: '删除项目' })
  @ApiResponse({ status: 200, description: '项目删除成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  @ApiResponse({ status: 404, description: '项目不存在' })
  async deleteProject(@Body() params: RemoveReqDto) {
    await this.projectService.syncDataDelete(params.id);
    return this.projectService.deleteProject(params);
  }

  @Post('find')
  @ApiOperation({ summary: '查询项目' })
  @ApiResponse({ status: 200, description: '查询成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  async findProject(@Body() filter: Partial<Project>) {
    return this.projectService.findProject(filter);
  }

  @Post('findAll')
  @ApiOperation({ summary: '查询所有项目' })
  @ApiResponse({ status: 200, description: '查询成功' })
  async findAllProject(@Body() filter: Partial<Project>) {
    return this.projectService.findAllProject(filter);
  }

}
