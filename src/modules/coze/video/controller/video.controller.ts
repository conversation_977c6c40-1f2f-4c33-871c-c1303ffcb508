import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { VideoService } from '../services/video.service';
import { Video } from '../entities/video.entity';
import { VideoReqDto } from '../dto/req/video.req.dto';
import { RemoveReqDto } from '../../../../utils/remove.req.dto';

@ApiTags('coze')
@Controller('coze/video')
export class VideoController {
  constructor(private readonly videoService: VideoService) {}

  // Video CRUD
  @Post('create')
  @ApiOperation({ summary: '创建视频' })
  async createVideo(@Body() video: Video) {
    return this.videoService.createVideo(video);
  }

  @Post('update')
  @ApiOperation({ summary: '更新视频' })
  async updateVideo(@Body() video: VideoReqDto) {
    return this.videoService.updateVideo(video.id, video);
  }

  @Post('remove')
  @ApiOperation({ summary: '删除视频' })
  async deleteVideo(@Body() params: RemoveReqDto) {
    return this.videoService.deleteVideo(params);
  }

  @Post('find')
  @ApiOperation({ summary: '查询视频' })
  async findVideo(@Body() filter: Partial<Video>) {
    return this.videoService.findVideo(filter);
  }

  @Post('buildVideo')
  @ApiOperation({ summary: '生成视频' })
  async buildVideo(
    @Body() params: { merchantId: string; date: string; limit: string },
  ) {
    return this.videoService.buildVideo(
      params.merchantId,
      params.date,
      params.limit,
    );
  }

  //   根据视频数据id生成视频
  @Post('buildVideoById')
  @ApiOperation({ summary: '根据视频数据id生成视频' })
  async buildVideoById(@Body() params: { id: string }) {
    return this.videoService.buildVideoById(params.id);
  }
}
