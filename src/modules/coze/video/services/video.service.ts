import { HttpException, HttpStatus, Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CommonService } from '../../../../utils/common.service';
import { Video } from '../entities/video.entity';
import { RemoveReqDto } from 'src/utils/remove.req.dto';
import { ZtBaseResDto } from 'src/utils/baseRes.dto';
import { VideoResDto } from '../dto/res/video.res.dto';
import { ProjectService } from '../../project/services/project.service';
import { HotService } from '../../hot/services/hot.service';

import { HumanService } from '../../human/services/human.service';
import { BenchmarkingService } from '../../benchmarking/services/benchmarking.service';
import { QaService } from '../../Qa/services/Qa.service';
import { CozeWorkflowManager } from '../../coze-workflow.manager';
import { IpService } from '../../ip/services/ip.service';
import { IpEntity } from '../../ip/entities/ip.entity';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { reqUser } from '../../../../utils/nameSpace';

@Injectable()
export class VideoService extends CommonService {
  constructor(
    @InjectRepository(Video)
    private readonly videoRepository: Repository<Video>,
    @Inject(REQUEST) request: Request,
    private projectService: ProjectService,
    private hotService: HotService,
    private humanService: HumanService,
    private benchmarkingService: BenchmarkingService,
    private qaService: QaService,
    private ipService: IpService, // Assuming this is the service for IP information
    private cozeWorkflowManager: CozeWorkflowManager,
  ) {
    super(request);
  }

  // Video CRUD
  async createVideo(video: Video | Video[]) {
    try {
      return this.createEntity<Video>(this.videoRepository, video, 'Video');
    } catch (error) {
      throw new HttpException(
        `Create Video failed: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async updateVideo(id: string, video: Partial<Video>): Promise<void> {
    return this.updateEntity(this.videoRepository, id, video, 'Video');
  }

  async deleteVideo(params: RemoveReqDto): Promise<void> {
    return this.deleteEntity(this.videoRepository, params, 'Video');
  }

  async findVideo(filter: Partial<Video>): Promise<ZtBaseResDto> {
    return this.findWithPagination(this.videoRepository, filter, VideoResDto);
  }

  async buildVideo(merchantId: string, date: string, limit: string) {
    //  获取商户信息
    const project = await this.projectService.findOne(merchantId);
    // 获取今天的Hot信息
    const hot = await this.hotService.findOne({ merchantId, date });

    // 获取全部的Human信息
    const human = await this.humanService.findAll({ merchantId });
    //  获取全部的QA信息
    const qa = await this.qaService.findAll({ merchantId });
    // 获取项目的IP形象信息
    const IpInfo: IpEntity = await this.ipService.findOne(
      project.selectedBenchmarkingAccountId,
    );
    // 获取全部的Benchmarking信息
    const benchmarking = await this.benchmarkingService.findAll({
      ipId: IpInfo.id,
    });

    console.log(56655, {
      human: human,
      benchmarking_list: benchmarking,
      date,
    });

    const res: any = await this.cozeWorkflowManager.generateVideoContent({
      account_positioning: project.accountPositioning,
      knowledge: project.knowledge,
      qa: qa,
      project_name: project.projectName,
      human: human,
      benchmarking_list: benchmarking,
      hot: hot,
      limit,
      date,
    });

    if (res && res.length > 0) {
      const arr = [];
      for (const item of res) {
        const video = new Video();
        video.merchantId = merchantId;
        video.text = item.text;
        video.humanId = item.human_id;
        video.title = item.title;
        video.titleDesc = item.title_desc;
        video.segment = JSON.stringify(item.story_board);
        video.date = date;
        arr.push(video);
      }
      return this.createVideo(arr);
    } else {
      throw new HttpException('生成失败', res.msg);
    }
  }

  //   根据视频数据id生成视频
  async buildVideoById(id: string) {
    const video = await this.videoRepository.findOne({ where: { id } });
    if (!video) {
      throw new HttpException('Video not found', HttpStatus.NOT_FOUND);
    }

    const videoUrl = await this.cozeWorkflowManager.generateVideoFile({
      segment: JSON.parse(video.segment),
      merchant_id: video.merchantId,
      human_id: video.humanId.toString(), // 将number转换为string
    });

    //   返回视频url，将url存入数据库
    if (videoUrl) {
      await this.updateVideo(id, { videoUrl });
      return {
        message: '视频生成成功',
        videoUrl,
      };
    } else {
      throw new HttpException('视频生成失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
