import { Column, Entity } from 'typeorm';
import { ZtBaseEntity } from '../../../../utils/base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

/**
 * 视频实体
 */
@Entity()
export class Video extends ZtBaseEntity {
  /**
   * 视频文案
   */
  @ApiProperty({ example: '视频文案内容', description: '视频文案' })
  @Column({ length: 5000 })
  @IsNotEmpty()
  text: string;

  /**
   * 日期
   */
  @ApiProperty({ example: '2024-03-20', description: '日期' })
  @Column()
  @IsNotEmpty()
  date: string;

  /**
   * 发布时间
   */
  @ApiProperty({ example: '2024-03-20 10:00:00', description: '发布时间' })
  @Column({ nullable: true })
  releaseTime: string;

  /**
   * 标题
   */
  @ApiProperty({ example: '视频标题', description: '标题' })
  @Column()
  @IsNotEmpty()
  title: string;

  /**
   * 视频描述
   */
  @ApiProperty({ example: '视频详细描述', description: '视频描述' })
  @Column()
  @IsNotEmpty()
  titleDesc: string;

  /**
   * 数字人id
   */
  @ApiProperty({ example: 1, description: '数字人id' })
  @Column({ type: 'varchar', length: 50 })
  @IsNotEmpty()
  humanId: number;

  /**
   * 项目id
   */
  @ApiProperty({ example: 'PROJECT001', description: '项目id' })
  @Column()
  @IsNotEmpty()
  merchantId: string;

  // /**
  //  * 选题
  //  */
  // @ApiProperty({ example: '视频选题', description: '选题' })
  // @Column()
  // @IsNotEmpty()
  // titlePre: string;
  //
  // /**
  //  * 选题描述
  //  */
  // @ApiProperty({ example: '选题详细描述', description: '选题描述' })
  // @Column()
  // @IsNotEmpty()
  // titleDescPre: string;

  /**
   * 视频地址
   */
  @ApiProperty({
    example: 'https://example.com/video.mp4',
    description: '视频地址',
  })
  @Column({ length: 500, nullable: true })
  videoUrl: string;

  /**
   * 视频脚本
   */
  @ApiProperty({ example: '视频脚本内容', description: '视频脚本' })
  @Column({ length: 5000 })
  @IsNotEmpty()
  segment: string;

  /**
   * 用户项目ID列表（用于项目权限过滤）
   * 存储为JSON字符串，包含用户有权限访问的项目ID列表
   */
  @ApiProperty({
    example: '["project1", "project2"]',
    description: '用户项目ID列表，用于权限过滤',
    required: false
  })
  @Column('text', { nullable: true })
  _userProjectIds?: string;
}
