// src/modules/coze/dto/req/video.req.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Column } from 'typeorm';
import { ZtBaseReqDto } from '../../../../../utils/baseReq.dto';

export class VideoReqDto extends ZtBaseReqDto {
  /**
   * 视频文案
   */
  @ApiProperty({ example: '视频文案内容', description: '视频文案' })
  @Column()
  @IsNotEmpty()
  @IsString()
  text: string;

  /**
   * 日期
   */
  @ApiProperty({ example: '2024-03-20', description: '日期' })
  @Column()
  @IsNotEmpty()
  @IsString()
  date: string;

  /**
   * 数字人id
   */
  @ApiProperty({ example: 1, description: '数字人id' })
  @IsOptional()
  humanId: number;

  /**
   * 发布时间
   */
  @ApiProperty({ example: '2024-03-20 10:00:00', description: '发布时间' })
  @IsOptional()
  @IsString()
  releaseTime: string;

  /**
   * 标题
   */
  @ApiProperty({ example: '视频标题', description: '标题' })
  @Column()
  @IsNotEmpty()
  @IsString()
  title: string;

  /**
   * 视频描述
   */
  @ApiProperty({ example: '视频详细描述', description: '视频描述' })
  @Column()
  @IsNotEmpty()
  @IsString()
  titleDesc: string;

  /**
   * 项目ID
   */
  @ApiProperty({ example: 'PROJECT001', description: '项目id' })
  @Column()
  @IsNotEmpty()
  @IsString()
  merchantId: string;
}
