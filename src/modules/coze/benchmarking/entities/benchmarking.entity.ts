import { Column, Entity } from 'typeorm';
import { ZtBaseEntity } from '../../../../utils/base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * Benchmarking 实体类
 *
 * 该实体用于存储和管理抖音视频的基准测试数据，包括视频信息、音频转写文本以及相关统计数据。
 * 主要用途是跟踪特定IP账号的视频表现和内容分析，为内容创作和营销策略提供数据支持。
 *
 * 继承自ZtBaseEntity，包含基础字段如id、createdAt、updatedAt等。
 */
@Entity()
export class Benchmarking extends ZtBaseEntity {
  /**
   * 抖音视频的唯一标识符
   *
   * 用于在抖音平台唯一识别一个视频，通常是一个数字字符串，格式如"7345678901234567890"
   * 该字段必须提供且不能为空，是进行数据关联和去重的关键字段
   */
  @ApiProperty({ example: '7345678901234567890', description: '抖音视频ID' })
  @Column()
  @IsNotEmpty()
  awemeId: string;


  /**
   * 抖音视频的创建时间
   * 存储视频在抖音平台的创建时间，格式为ISO 8601字符串
   * 该字段可以为空，类型为timestamp
   * 注意：该字段的时间格式为UTC时间
   */
  @ApiProperty({ example: '2023-01-01 12:00:00', description: '视频创建时间' })
  @Column({ nullable: true })
  @IsOptional()
  createTime: string;

  /**
   * 视频的描述文本
   *
   * 存储视频的标题或描述内容，对应抖音视频的desc字段
   * 该字段可以为空，类型为text以支持长文本内容
   */
  @ApiProperty({ example: '这是一个抖音视频描述', description: '视频描述' })
  @Column({ type: 'text', nullable: true })
  description: string;

  /**
   * 视频标题
   *
   * 存储视频的简短标题，便于在列表中展示
   * 该字段可以为空，类型为text以支持长文本内容
   */
  @ApiProperty({ example: '视频标题', description: '视频标题' })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  title: string;


  /**
   * 视频时长
   *
   * 存储视频的播放时长，单位为毫秒
   * 该字段可选，类型为integer
   */
  @ApiProperty({ example: 180000, description: '视频时长(毫秒)' })
  @Column({ nullable: true })
  @IsOptional()
  duration: number;

  /**
   * 视频的URL地址
   *
   * 存储视频的访问地址，用于直接播放或下载视频内容
   * 该字段可以为空，类型为text以支持长URL
   */
  @ApiProperty({
    example: 'https://example.com/video.mp4',
    description: '视频URL',
  })
  @Column({ type: 'text', nullable: true })
  videoUrl: string;

  /**
   * 音频的URL地址
   *
   * 存储视频的音频部分的访问地址，用于音频内容提取和语音识别
   * 该字段可以为空，类型为text以支持长URL
   */
  @ApiProperty({
    example: 'https://example.com/audio.mp3',
    description: '音频URL',
  })
  @Column({ type: 'text', nullable: true })
  audioUrl: string;

  /**
   * 视频作者的昵称
   *
   * 存储创建该视频的抖音用户的昵称，用于追踪内容创作者
   * 该字段必须提供且不能为空
   */
  @ApiProperty({ example: '用户昵称', description: '作者昵称' })
  @Column()
  @IsNotEmpty()
  upName: string;

  /**
   * 内容类型
   *
   * 标识该内容是视频还是图片，目前支持的值为 'video' 或 'image'
   * 该字段必须符合枚举限制，且是字符串类型
   */
  @ApiProperty({
    example: 'video',
    description: '内容类型',
    enum: ['video', 'image'],
  })
  @Column()
  @IsEnum(['video', 'image'])
  @IsString()
  type: string;


  /**
   * 音频转写文本
   *
   * 存储通过语音识别从视频音频中提取的文本内容
   * 若无语音或识别失败，则填充为"无语音"
   * 该字段可以为空，类型为text以支持长文本内容
   */
  @ApiProperty({ example: '这是一个抖音视频描述', description: '音频文本' })
  @Column({ type: 'text', nullable: true })
  @IsString()
  audioText: string;

  //视频分析
  /**
   * 视频分析
   *
   */
  @ApiProperty({
    example: '视频分析',
    description: '视频分析',
  })
  @Column({ type: 'text', nullable: true })
  @IsOptional()
  videoAnalysis: string;


  /**
   * IP账号的唯一标识符
   *
   * 关联到IP实体的外键，标识该视频属于哪个IP账号
   * 该字段必须提供且不能为空，用于分组和关联查询
   */
  @ApiProperty({ example: '7345678901234567890', description: 'ip的ID' })
  @Column()
  @IsNotEmpty()
  ipId: string;

  /**
   * 点赞数量
   *
   * 记录该视频在抖音平台获得的点赞总数，反映内容受欢迎程度
   * 默认值为0，可选字段
   */
  @ApiProperty({ example: 1000, description: '点赞数' })
  @Column({ default: 0 })
  @IsOptional()
  diggCount: number;

  /**
   * 评论数量
   *
   * 记录该视频在抖音平台获得的评论总数，反映互动程度
   * 默认值为0，可选字段
   */
  @ApiProperty({ example: 500, description: '评论数' })
  @Column({ default: 0 })
  @IsOptional()
  commentCount: number;

  /**
   * 收藏数量
   *
   * 记录该视频在抖音平台被收藏的次数，反映内容质量和用户保存意愿
   * 默认值为0，可选字段
   */
  @ApiProperty({ example: 200, description: '收藏数' })
  @Column({ default: 0 })
  @IsOptional()
  collectCount: number;

  /**
   * 分享数量
   *
   * 记录该视频在抖音平台被分享的次数，反映内容传播能力
   * 默认值为0，可选字段
   */
  @ApiProperty({ example: 100, description: '分享数' })
  @Column({ default: 0 })
  @IsOptional()
  shareCount: number;

  /**
   * 播放数量
   *
   * 记录该视频在抖音平台被播放的总次数，反映内容曝光度
   * 默认值为0，可选字段
   */
  @ApiProperty({ example: 5000, description: '播放数' })
  @Column({ default: 0 })
  @IsOptional()
  playCount: number;
}
