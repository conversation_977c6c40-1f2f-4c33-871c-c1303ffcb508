/**
 * 基准测试统计结果接口
 * 
 * 用于表示benchmarking操作的统计结果，包括成功状态、消息和数据统计
 */
export interface BenchmarkingStatsResult {
  /** 操作是否成功 */
  success: boolean;
  
  /** 操作结果消息 */
  message: string;
  
  /** 统计数据 */
  data: {
    /** 总记录数 */
    total: number;
    
    /** 新增记录数 */
    new?: number;
    
    /** 保留的记录数 */
    kept?: number;
    
    /** 更新的记录数 */
    updated?: number;
    
    /** 删除的记录数 */
    deleted?: number;
    
    /** 进行语音转写的记录数 */
    transcribed?: number;
    
    /** 正在进行语音转写的记录数 */
    transcribing?: number;
    
    /** 账号定位分析结果 */
    positioning?: any;
  };
} 