import { Body, Controller, Post, Get, Query, Param } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Benchmarking } from '../entities/benchmarking.entity';
import { BenchmarkingReqDto } from '../dto/req/benchmarking.req.dto';
import { RemoveReqDto } from '../../../../utils/remove.req.dto';
import { BenchmarkingService } from '../services/benchmarking.service';
import { BenchmarkingGetVideoReqDto } from '../dto/req/benchmarkingGetVideo.req.dto';
import { RefreshBenchmarkingReqDto } from '../dto/req/refreshBenchmarking.req.dto';
import { BenchmarkingStatsResult } from '../types/benchmarking.types';
import { BenchmarkingResDto } from '../dto/res/benchmarking.res.dto';
import { UpdatePositioningDto } from '../dto/req/update.positioning.req.dto';
import { GenerateAudioTextDto } from '../dto/req/generate.audiotext.req.dto';

@ApiTags('coze/benchmarking')
@Controller('coze/benchmarking')
export class BenchmarkingController {
  constructor(private readonly benchmarkingService: BenchmarkingService) {
  }

  @Post('create')
  @ApiOperation({ summary: '创建 Benchmarking' })
  async createBenchmarking(@Body() benchmarking: Benchmarking) {
    return this.benchmarkingService.createBenchmarking(benchmarking);
  }

  @Post('update')
  @ApiOperation({ summary: '更新 Benchmarking' })
  async updateBenchmarking(@Body() benchmarking: BenchmarkingReqDto) {
    return this.benchmarkingService.updateBenchmarking(
      benchmarking.id,
      benchmarking,
    );
  }

  @Post('remove')
  @ApiOperation({ summary: '删除 Benchmarking' })
  async deleteBenchmarking(@Body() params: RemoveReqDto) {
    return this.benchmarkingService.deleteBenchmarking(params);
  }

  @Post('find')
  @ApiOperation({ summary: '查询 Benchmarking' })
  async findBenchmarking(@Body() filter: Partial<BenchmarkingReqDto>) {
    // 添加默认排序，按创建时间的最新排序
    if (!filter.sort) {
      filter.sort = {
        sortBy: 'createTime',
        sortOrder: 'descend'  // descend表示降序，最新的排在前面
      };
      console.log('应用默认排序: 按创建时间降序（最新的排在前面）');
    }
    
    return this.benchmarkingService.findBenchmarking(filter);
  }

  @Post('getBenchmarkingByUrl')
  @ApiOperation({ summary: '根据url查询 Benchmarking' })
  async getBenchmarkingByUrl(@Body() params: BenchmarkingGetVideoReqDto) {
    const result = await this.benchmarkingService.getBenchmarkingByUrl(params);
    
    // 返回标准格式响应
    return {
      code: result.success ? 200 : 400,
      data: result.data,
      msg: result.message
    };
  }

  @Post('refresh')
  @ApiOperation({ summary: '刷新IP账号的视频数据' })
  async refreshBenchmarkingData(@Body() params: RefreshBenchmarkingReqDto) {
    const result = await this.benchmarkingService.refreshBenchmarkingData(params.ipId, params.limit);
    
    // 返回标准格式响应
    return {
      code: result.success ? 200 : 400,
      data: result.data,
      msg: result.message
    };
  }

  @Post('details')
  @ApiOperation({ summary: '获取benchmarking列表详情' })
  @ApiResponse({ status: 200, description: '成功', type: BenchmarkingResDto })
  async getBenchmarkingDetails(@Body() params: { ipId: string }) {
    try {
      const ipId = params.ipId;
      
      // 只获取IP账号信息以获取定位数据，不包含视频列表
      const ipAccount = await this.benchmarkingService.getIpAccountDetails(ipId);
      
      return {
        code: 200,
        data: {
          ipAccount: ipAccount || null
        },
        msg: '获取数据成功'
      };
    } catch (error) {
      console.error('获取benchmarking详情失败:', error);
      return {
        code: 400,
        data: null,
        msg: `获取数据失败: ${error.message}`
      };
    }
  }

  @Get('find')
  @ApiOperation({ summary: '查询视频数据' })
  @ApiResponse({ status: 200, description: '成功', type: BenchmarkingResDto })
  async find(@Query() query: any) {
    // 处理查询参数
    const processedQuery = { ...query };
    
    // 确保ipId参数被正确处理
    if (processedQuery.ipId) {
      // 保持ipId为字符串类型
      processedQuery.ipId = processedQuery.ipId.toString();
      
      console.log(`使用ipId过滤: ${processedQuery.ipId}`);
    }
    
    // 处理分页参数
    if (processedQuery.pageIndex) {
      processedQuery.pageIndex = parseInt(processedQuery.pageIndex, 10);
    }
    
    if (processedQuery.pageSize) {
      processedQuery.pageSize = parseInt(processedQuery.pageSize, 10);
    }
    
    // 添加默认排序，按创建时间的最新排序
    if (!processedQuery.sort) {
      processedQuery.sort = {
        sortBy: 'createTime',
        sortOrder: 'descend'  // descend表示降序，最新的排在前面
      };
      console.log('应用默认排序: 按创建时间降序（最新的排在前面）');
    }
    
    return this.benchmarkingService.findBenchmarking(processedQuery);
  }
  
  @Post('findAll')
  @ApiOperation({ summary: '查询全部视频数据（无分页，用于按钮状态判断）' })
  @ApiResponse({ status: 200, description: '成功', type: BenchmarkingResDto })
  async findAll(@Body() params: any) {
    // 处理查询参数
    const processedQuery = { ...params };
    
    // 确保ipId参数被正确处理
    if (processedQuery.ipId) {
      // 保持ipId为字符串类型
      processedQuery.ipId = processedQuery.ipId.toString();
      
      console.log(`使用ipId过滤(全量): ${processedQuery.ipId}`);
    }
    
    // 设置非常大的pageSize，以获取所有数据
    processedQuery.pageIndex = 1;
    processedQuery.pageSize = 9999; // 设置一个足够大的数字来获取全部数据
    
    // 添加默认排序，按创建时间的最新排序
    if (!processedQuery.sort) {
      processedQuery.sort = {
        sortBy: 'createTime',
        sortOrder: 'descend'
      };
    }
    
    return this.benchmarkingService.findBenchmarking(processedQuery);
  }

  @Post('update-positioning')
  @ApiOperation({ summary: '更新账号定位' })
  @ApiResponse({ status: 200, description: '成功', type: Object })
  async updatePositioning(@Body() body: UpdatePositioningDto) {
    const result = await this.benchmarkingService.updatePositioning(body);
    
    // 返回标准格式响应
    return {
      code: result.success ? 200 : 400,
      data: result.data,
      msg: result.message
    };
  }

  @Post('generate-audio-text')
  @ApiOperation({ summary: '生成视频文案' })
  @ApiResponse({ status: 200, description: '成功', type: Object })
  async generateAudioText(@Body() body: GenerateAudioTextDto) {
    const result = await this.benchmarkingService.generateAudioText(body);
    
    // 返回标准格式响应
    return {
      code: result.success ? 200 : 400,
      data: result.data || {},
      msg: result.message
    };
  }
}
