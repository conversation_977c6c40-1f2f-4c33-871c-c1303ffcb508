// src/modules/coze/dto/res/benchmarking.res.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { Benchmarking } from '../../entities/benchmarking.entity';

/**
 * 基准测试响应DTO
 *
 * 用于返回视频数据查询结果
 */
export class BenchmarkingDataDto {
  /**
   * 当前页码
   */
  @ApiProperty({
    description: '当前页码',
    example: 1,
  })
  pageIndex: number;

  /**
   * 总记录数
   */
  @ApiProperty({
    description: '总记录数',
    example: 10,
  })
  total: number;

  /**
   * 每页条数
   */
  @ApiProperty({
    description: '每页条数',
    example: 10,
  })
  pageSize: number;

  /**
   * 查询结果数据
   */
  @ApiProperty({
    description: '查询结果数据',
    type: () => [Benchmarking],
  })
  data: Benchmarking[];
}

/**
 * 基准测试响应DTO
 */
export class BenchmarkingResDto extends Benchmarking {
  /**
   * 构造函数
   * @param user - 用户实体对象
   */
  constructor(user: any) {
    super();
    Object.assign(this as any, user);
  }
}