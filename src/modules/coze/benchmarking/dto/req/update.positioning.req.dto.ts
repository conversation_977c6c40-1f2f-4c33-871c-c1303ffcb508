import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * 更新账号定位请求DTO
 * 
 * 用于接收更新IP账号定位分析的请求参数
 */
export class UpdatePositioningDto {
  /**
   * IP账号ID
   * 
   * 需要更新定位分析的IP账号的唯一标识符
   * 该字段必须提供且不能为空
   */
  @ApiProperty({
    description: 'IP账号ID',
    example: '7345678901234567890',
    required: true
  })
  @IsNotEmpty({ message: 'IP账号ID不能为空' })
  @IsString({ message: 'IP账号ID必须是字符串' })
  ipId: string;
} 