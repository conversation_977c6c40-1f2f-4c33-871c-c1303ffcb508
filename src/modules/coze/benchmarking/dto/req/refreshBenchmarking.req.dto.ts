import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber, Min, Max } from 'class-validator';

export class RefreshBenchmarkingReqDto {
  @ApiProperty({ description: 'IP账号ID', example: '1234567890' })
  @IsNotEmpty({ message: 'IP账号ID不能为空' })
  @IsString({ message: 'IP账号ID必须是字符串' })
  ipId: string;

  @ApiProperty({ description: '限制获取的视频数量，默认为20', example: 20, required: false })
  @IsOptional()
  @IsNumber({}, { message: '视频数量必须是数字' })
  @Min(1, { message: '视频数量最小为1' })
  @Max(100, { message: '视频数量最大为100' })
  limit?: number;
} 