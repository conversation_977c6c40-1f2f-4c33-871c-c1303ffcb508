// src/modules/coze/dto/req/benchmarking.req.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Column } from 'typeorm';
import { ZtBaseReqDto } from '../../../../../utils/baseReq.dto';

export class BenchmarkingReqDto extends ZtBaseReqDto {
  /**
   * UP主名称
   */
  @ApiProperty({ example: '某UP主', description: 'UP主名称' })
  @IsNotEmpty()
  upName: string;

  /**
   * 视频/图片URL
   */
  @ApiProperty({
    example:
      'https://v3-web.douyinvod.com/14adf3f8066a8c48ea22065ede463217/683208d7/video/tos/cn/tos-cn-ve-0015c800/oAEeuLcvAec7UGl5R86BV85lCKIBXO5GACCegG/?a=6383&ch=10010&cr=3&dr=0&lr=all&cd=0%7C0%7C0%7C3&cv=1&br=1200&bt=1200&cs=0&ds=3&ft=pEaFx4hZffPdr5~-v1jNvAq-antLjrKBoKxCRkaOkFQ5ejVhWL6&mime_type=video_mp4&qs=0&rc=OWY8OTRoOGk6OTpnPDM1NEBpamg4anM5cjVxdjMzNGkzM0AtYWFeXjI0Ni4xMmAzMS0zYSNjYmFjMmRrNGtgLS1kLTBzcw%3D%3D&btag=80000e00028000&cquery=100B_100x_100z_100o_101r&dy_q=1748098501&feature_id=aa7df520beeae8e397df15f38df0454c&l=202505242255011AA9A89D4F01FBC0485B',
    description: '视频或图片URL',
  })
  @IsNotEmpty()
  url: string;

  /**
   * 描述文本
   */
  @ApiProperty({ example: '视频详细描述', description: '描述文本' })
  @IsNotEmpty()
  description: string;

  /**
   * 视频文案
   */
  @ApiProperty({ example: '视频配音文案', description: '视频文案' })
  @IsOptional()
  audioText?: string;

  /**
   * IP的ID
   * 用于过滤特定IP账号的视频数据
   * 在GET请求中通过query参数传递
   */
  @ApiProperty({ 
    example: '550e8400-e29b-41d4-a716-446655440000', 
    description: 'IP账号ID，用于过滤特定IP的视频',
    required: false 
  })
  @IsOptional()
  @IsString()
  ipId?: string;
}
