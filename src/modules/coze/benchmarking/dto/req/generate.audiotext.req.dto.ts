import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * 生成视频文案请求DTO
 */
export class GenerateAudioTextDto {
  /**
   * 视频ID
   */
  @ApiProperty({
    example: '65e52f18e3d37d9bb80dc7c4',
    description: '视频记录的ID',
  })
  @IsNotEmpty()
  @IsString()
  id: string;

  /**
   * 音频URL
   */
  @ApiProperty({
    example: 'https://example.com/audio.mp3',
    description: '音频URL',
    required: false
  })
  @IsString()
  audioUrl?: string;
} 