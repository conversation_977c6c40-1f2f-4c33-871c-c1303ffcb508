import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsNumber, IsOptional, IsString } from 'class-validator';

export class BenchmarkingGetVideoReqDto {
  @ApiProperty({
    description: '获取数量限制(0表示无限制)',
    example: 100,
    default: 100,
  })
  @IsOptional()
  @IsNumber()
  limit?: number = 100;

  @ApiProperty({
    description: '内容类型',
    enum: ['post', 'like'],
    default: 'post',
  })
  @IsOptional()
  @IsString()
  @IsIn(['post', 'like'])
  type?: string = 'post';

  @ApiProperty({
    description: 'ip定位的ID',
    example: '5f9f1b0b0b0b0b0b0b0b0b0b',
  })
  @IsString()
  ipId?: string;
}
