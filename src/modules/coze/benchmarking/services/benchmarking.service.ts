import { forwardRef, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { ZtBaseResDto } from '../../../../utils/baseRes.dto';
import { Benchmarking } from '../entities/benchmarking.entity';
import { BenchmarkingResDto } from '../dto/res/benchmarking.res.dto';
import { RemoveReqDto } from '../../../../utils/remove.req.dto';
import { CommonService } from 'src/utils/common.service';
import { DouyinService } from '../../../douyin/services/douyin.service';
import { DyService } from '../../../dy/services/dy.service';
import { CozeWorkflowManager } from '../../coze-workflow.manager';
import { BenchmarkingGetVideoReqDto } from '../dto/req/benchmarkingGetVideo.req.dto';
import { IpService } from '../../ip/services/ip.service';
import { BenchmarkingReqDto } from '../dto/req/benchmarking.req.dto';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { BenchmarkingStatsResult } from '../types/benchmarking.types';

@Injectable()
export class BenchmarkingService extends CommonService {
  private readonly logger = new Logger(BenchmarkingService.name);

  constructor(
    @InjectRepository(Benchmarking)
    private benchmarkingRepository: Repository<Benchmarking>,
    private douyinService: DouyinService,
    private dyService: DyService,
    @Inject(forwardRef(() => IpService))
    private ipService: IpService,
    @Inject(REQUEST) request: Request,
    private cozeWorkflowManager: CozeWorkflowManager,
  ) {
    super(request);
  }

  // Benchmarking CRUD
  async createBenchmarking(benchmarking: Benchmarking | Benchmarking[]): Promise<void> {
    return this.createEntity<Benchmarking>(
      this.benchmarkingRepository,
      benchmarking,
      'Benchmarking',
    );
  }

  async updateBenchmarking(
    id: string,
    benchmarking: Partial<Benchmarking>,
  ): Promise<void> {
    return this.updateEntity(
      this.benchmarkingRepository,
      id,
      benchmarking,
      'Benchmarking',
    );
  }

  async deleteBenchmarking(params: RemoveReqDto): Promise<void> {
    return this.deleteEntity(
      this.benchmarkingRepository,
      params,
      'Benchmarking',
    );
  }

  async findBenchmarking(
    filter: Partial<BenchmarkingReqDto>,
  ): Promise<ZtBaseResDto> {
    return this.findWithPagination(
      this.benchmarkingRepository,
      filter,
      BenchmarkingResDto,
      { authMode: 'createdBy' }
    );
  }

  async getBenchmarkingByUrl(params: BenchmarkingGetVideoReqDto): Promise<BenchmarkingStatsResult> {
    // 根据IP的id获取数据
    const ip = await this.ipService.findOne(params.ipId);
    if (!ip) {
      this.logger.error(`找不到ID为 ${params.ipId} 的IP账号`);
      throw new NotFoundException(`找不到ID为 ${params.ipId} 的IP账号`);
    }

    if (!ip.benchmarkingUrl) {
      this.logger.warn(`IP ID ${params.ipId} 没有设置抖音URL`);
      throw new Error(`IP ID ${params.ipId} 没有设置抖音URL`);
    }

    try {
      // 获取用户发布的视频，按时间倒序（最新的在前）
      const res = await this.douyinService.getUpInfoServer({
        ...params,
        user: ip.benchmarkingUrl,
      });

      // 过滤出有音频的视频（保持原始顺序 - 从最新到最旧）
      const videosWithAudio = res.filter(item => Boolean(item.audioUrl));

      if (!videosWithAudio.length) {
        return {
          success: false,
          message: '没有找到带音频的视频数据',
          data: { total: 0 },
        };
      }

      // 如果需要限制处理的视频数量
      const processVideos = params.limit && videosWithAudio.length > params.limit
        ? videosWithAudio.slice(0, params.limit)
        : videosWithAudio;

      this.logger.log(`根据limit=${params.limit}参数，将处理最新的 ${processVideos.length} 条视频，总数: ${videosWithAudio.length}`);

      // 先查询数据库中已经有记录的视频
      const existingRecords = await this.benchmarkingRepository.find({
        where: { ipId: params.ipId },
        select: ['awemeId', 'audioText', 'id'],
      });

      // 创建已存在awemeId的映射
      const existingAwemeIdMap = existingRecords.reduce<Record<string, any>>((acc, record) => {
        acc[record.awemeId] = record;
        return acc;
      }, {});

      // 获取要删除的记录ID列表 - 只删除那些不再需要的记录
      const allAwemeIds = processVideos.map(item => item.awemeId);
      const recordsToDelete = existingRecords
        .filter(record => !allAwemeIds.includes(record.awemeId))
        .map(record => record.id);

      // 过滤出需要进行语音识别的视频
      const needTranscribeItems = processVideos.filter(item => !existingAwemeIdMap[item.awemeId]);

      this.logger.log(`需要进行语音识别的视频数量: ${needTranscribeItems.length}, 总视频数量: ${processVideos.length}`);

      // 只对需要识别的视频调用工作流
      let audioResults = [];
      if (needTranscribeItems.length > 0) {
        try {
          audioResults = await this.cozeWorkflowManager.extractAudioText({
            input: needTranscribeItems.map((data) => ({
              audio_url: data.audioUrl,
            })),
          });
        } catch (error) {
          this.logger.error(`语音识别出错: ${error.message}`);
          // 继续执行，将无法识别的标记为"无语音"
        }
      }

      const benchmarkingEntities: Benchmarking[] = [];

      for (const item of processVideos) {
        // 跳过已存在的记录
        if (existingAwemeIdMap[item.awemeId]) {
          continue;
        }

        const benchmarking = new Benchmarking();
        benchmarking.awemeId = item.awemeId;
        benchmarking.description = item.description || '';
        benchmarking.videoUrl = item.videoUrl || '';
        benchmarking.audioUrl = item.audioUrl || '';
        benchmarking.upName = item.upName || '';
        benchmarking.type = item.type || 'video';
        benchmarking.ipId = params.ipId;

        // 添加统计数据
        benchmarking.diggCount = item.diggCount || 0;
        benchmarking.commentCount = item.commentCount || 0;
        benchmarking.collectCount = item.collectCount || 0;
        benchmarking.shareCount = item.shareCount || 0;

        // 处理语音识别结果
        const audioInfo = audioResults.find(
          (result: any) => result?.audio_url === item.audioUrl,
        );

        benchmarking.audioText = audioInfo?.text || '无语音';

        // 只有当语音文本长度超过50才添加到实体列表
        if (benchmarking.audioText.length > 50) {
          benchmarkingEntities.push(benchmarking);
        }
      }

      // 包装为事务操作
      try {
        // 如果有需要删除的记录，先删除
        if (recordsToDelete.length > 0) {
          await this.benchmarkingRepository.delete(recordsToDelete);
          this.logger.log(`已删除 ${recordsToDelete.length} 条旧记录`);
        }

        // 如果有新实体数据，保存到数据库
        if (benchmarkingEntities.length > 0) {
          await this.createBenchmarking(benchmarkingEntities);
          this.logger.log(`成功添加 ${benchmarkingEntities.length} 条新视频数据`);
        }
      } catch (dbError) {
        this.logger.error(`数据库操作失败: ${dbError.message}`, dbError.stack);
        throw new Error(`数据库操作失败: ${dbError.message}`);
      }

      return {
        success: true,
        message: `成功更新数据：保留 ${existingRecords.length - recordsToDelete.length} 条现有记录，添加 ${benchmarkingEntities.length} 条新记录，删除 ${recordsToDelete.length} 条旧记录`,
        data: {
          total: existingRecords.length - recordsToDelete.length + benchmarkingEntities.length,
          new: benchmarkingEntities.length,
          kept: existingRecords.length - recordsToDelete.length,
          deleted: recordsToDelete.length,
        },
      };
    } catch (error) {
      this.logger.error(`获取抖音数据失败: ${error.message}`, error.stack);
      throw new Error(`获取抖音数据失败: ${error.message}`);
    }
  }

  async findAll(param: { ipId: string }): Promise<Benchmarking[]> {
    return this.findAllEntities(
      this.benchmarkingRepository,
      { ipId: param.ipId } as Partial<Benchmarking>,
      { sortBy: 'createdAt', sortOrder: 'descend' },
      { authMode: 'createdBy' }
    );
  }

  /**
   * 刷新IP账号的视频数据
   * 根据IP ID获取抖音数据并更新benchmarking
   * 现使用DyModule的服务来更新数据
   * @param ipId IP账号的ID
   * @param limit 限制获取的视频数量，默认为20
   * @returns 更新结果
   */
  async refreshBenchmarkingData(ipId: string, limit?: number): Promise<BenchmarkingStatsResult> {
    this.logger.log(`开始刷新IP ID为 ${ipId} 的视频数据, 数量限制: ${limit || 20}`);

    // 获取IP信息
    const ip = await this.ipService.findOne(ipId);
    if (!ip) {
      this.logger.error(`找不到ID为 ${ipId} 的IP账号`);
      throw new NotFoundException(`找不到ID为 ${ipId} 的IP账号`);
    }

    // 验证是否有抖音URL
    if (!ip.benchmarkingUrl || !ip.benchmarkingUrl.includes('douyin.com')) {
      this.logger.warn(`IP ID ${ipId} 没有有效的抖音URL: ${ip.benchmarkingUrl}`);
      throw new Error('无效的抖音URL，无法获取视频数据');
    }

    try {
      // 使用DyService获取最新的limit条账号数据
      const accountData = await this.dyService.getAccountData({
        user_url: ip.benchmarkingUrl,
        tab: 'post', // 获取用户发布的视频
        count: limit || 20, // 使用传入的limit参数或默认值
        pages: 1, // 只获取一页数据
      });

      if (!accountData?.items?.length) {
        this.logger.warn(`未从DyModule获取到视频数据`);
        return {
          success: false,
          message: '没有找到可用的视频数据',
          data: { total: 0 },
        };
      }

      // 过滤有音频地址的数据（保持原始顺序 - 从最新到最旧）
      const filteredItems = accountData.items.filter(item => Boolean(item.audioUrl));

      if (!filteredItems.length) {
        this.logger.warn(`获取的视频中没有包含音频的数据`);
        return {
          success: false,
          message: '没有找到带音频的视频数据',
          data: { total: 0 },
        };
      }

      // 如果需要限制处理的视频数量
      const processItems = limit && filteredItems.length > limit
        ? filteredItems.slice(0, limit)
        : filteredItems;

      this.logger.log(`处理最新的 ${processItems.length}/${filteredItems.length} 条视频`);

      // 获取所有视频的awemeId
      const awemeIds = processItems.map(item => item.id);

      // 查询数据库中已经存在的记录
      const existingRecords = await this.benchmarkingRepository.find({
        where: { ipId, awemeId: In(awemeIds) },
      });

      // 创建已存在awemeId的映射，方便快速查找
      const existingAwemeIdMap = existingRecords.reduce<Record<string, Benchmarking>>((acc, record) => {
        acc[record.awemeId] = record;
        return acc;
      }, {});

      // 分类处理项目
      const newItems: Benchmarking[] = []; // 完全新的记录
      const updateItems: Benchmarking[] = []; // 需要更新的记录
      const needTranscribeItems: Array<{ item: any, record: Benchmarking }> = []; // 需要进行语音识别的记录

      // 将抖音时间戳转换为格式化的日期时间字符串
      const formatTimestamp = (timestamp: number): string => {
        if (!timestamp) return '';

        try {
          // 创建日期对象（乘以1000转换为毫秒）
          const date = new Date(timestamp * 1000);

          // 获取年月日
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');

          // 获取时分秒
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          const seconds = String(date.getSeconds()).padStart(2, '0');

          // 组合成格式化的字符串
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        } catch (error) {
          this.logger.error(`时间戳转换失败: ${error.message}`, error.stack);
          return '';
        }
      };

      // 遍历过滤后的抖音数据
      for (const item of processItems) {
        const existingRecord = existingAwemeIdMap[item.id];

        // 格式化创建时间
        const formattedCreateTime = formatTimestamp(item.create_time);

        // 如果记录存在
        if (existingRecord) {
          // 只更新从抖音获取的数据字段，保留已生成的数据相关字段
          existingRecord.description = item.desc || existingRecord.description;
          existingRecord.videoUrl = item.video_url || existingRecord.videoUrl;
          existingRecord.audioUrl = item.audioUrl || existingRecord.audioUrl;
          existingRecord.upName = item.author?.nickname || existingRecord.upName || '';
          existingRecord.type = item.type || existingRecord.type || 'video';
          existingRecord.createTime = formattedCreateTime || existingRecord.createTime;
          // 添加视频时长字段(毫秒)
          existingRecord.duration = item.duration || existingRecord.duration;

          // 更新统计数据
          existingRecord.diggCount = item.statistics?.digg_count || existingRecord.diggCount || 0;
          existingRecord.commentCount = item.statistics?.comment_count || existingRecord.commentCount || 0;
          existingRecord.collectCount = item.statistics?.collect_count || existingRecord.collectCount || 0;
          existingRecord.shareCount = item.statistics?.share_count || existingRecord.shareCount || 0;
          // 播放数可能不在 statistics 中，使用现有值
          if (item.statistics && 'play_count' in item.statistics) {
            existingRecord.playCount = (item.statistics as any).play_count || existingRecord.playCount || 0;
          }

          // 添加到更新列表
          updateItems.push(existingRecord);

          // 如果没有audioText字段，则添加到需要语音识别的列表
          if (!existingRecord.audioText || existingRecord.audioText === '无语音' || existingRecord.audioText.trim() === '') {
            existingRecord.audioText = '生成中...'; // 先设置为生成中状态
            needTranscribeItems.push({
              item,
              record: existingRecord,
            });
          }
        } else {
          // 创建新记录
          const newRecord = new Benchmarking();
          newRecord.awemeId = item.id;
          newRecord.description = item.desc || '';
          newRecord.videoUrl = item.video_url || '';
          newRecord.audioUrl = item.audioUrl || '';
          newRecord.upName = item.author?.nickname || '';
          newRecord.type = item.type || 'video';
          newRecord.ipId = ipId;
          newRecord.audioText = '生成中...'; // 先设置为生成中状态
          newRecord.createTime = formattedCreateTime;
          // 添加视频时长字段(毫秒)
          newRecord.duration = item.duration || 0;

          // 添加统计数据
          newRecord.diggCount = item.statistics?.digg_count || 0;
          newRecord.commentCount = item.statistics?.comment_count || 0;
          newRecord.collectCount = item.statistics?.collect_count || 0;
          newRecord.shareCount = item.statistics?.share_count || 0;
          // 播放数可能不在 statistics 中，使用现有值
          if (item.statistics && 'play_count' in item.statistics) {
            newRecord.playCount = (item.statistics as any).play_count || 0;
          }

          // 添加到新记录列表
          newItems.push(newRecord);

          // 添加到需要语音识别的列表
          needTranscribeItems.push({
            item,
            record: newRecord,
          });
        }
      }

      // 首先保存所有实体，让前端能立即看到数据
      const entities: Benchmarking[] = [...newItems, ...updateItems];

      // 保存实体数据
      if (entities.length > 0) {
        try {
          await this.benchmarkingRepository.save(entities);
          this.logger.log(`成功初始保存数据：更新 ${updateItems.length} 条记录，添加 ${newItems.length} 条新记录`);
        } catch (dbError) {
          this.logger.error(`数据库初始保存失败: ${dbError.message}`, dbError.stack);
          throw new Error(`数据库初始保存失败: ${dbError.message}`);
        }
      }

      // 异步处理需要语音识别的项目
      if (needTranscribeItems.length > 0) {
        this.logger.log(`开始异步处理 ${needTranscribeItems.length} 个视频的文案生成`);

        // 使用processAudioTextAsync方法处理语音识别，但不触发账号定位更新
        setTimeout(() => {
          this.processAudioTextAsync(needTranscribeItems)
            .catch(error => this.logger.error(`异步处理文案失败: ${error.message}`, error.stack));
        }, 0);
      }

      // 明确表示此接口不会触发账号定位更新
      this.logger.log(`视频数据更新完成，根据要求不触发账号定位更新`);

      // 立即返回结果，不等待异步处理完成
      return {
        success: true,
        message: `成功获取数据：更新 ${updateItems.length} 条现有记录，添加 ${newItems.length} 条新记录，${needTranscribeItems.length} 条记录正在生成文案`,
        data: {
          total: entities.length,
          new: newItems.length,
          updated: updateItems.length,
          transcribing: needTranscribeItems.length,
        },
      };
    } catch (error) {
      this.logger.error(`刷新视频数据失败: ${error.message}`, error.stack);
      throw new Error(`刷新视频数据失败: ${error.message}`);
    }
  }

  /**
   * 更新账号分析
   * 调用Coze工作流进行账号分析
   * @param params 包含IP账号ID的参数对象
   * @returns 工作流的响应结果
   */
  async updatePositioning(params: { ipId: string }): Promise<BenchmarkingStatsResult> {
    const { ipId } = params;

    // 1. 检查IP账号是否存在
    const ipAccount = await this.ipService.findOne(ipId);
    if (!ipAccount) {
      this.logger.error(`找不到ID为 ${ipId} 的IP账号`);
      throw new NotFoundException(`找不到ID为 ${ipId} 的IP账号`);
    }

    // 2. 获取该IP账号的视频列表
    const videoList = await this.findAll({ ipId });
    if (!videoList || videoList.length === 0) {
      this.logger.warn(`IP账号 ${ipId} 没有关联的视频数据`);
      return {
        success: false,
        message: '没有找到视频数据，请先更新视频再进行账号定位',
        data: { total: 0 },
      };
    }

    this.logger.log(`开始为IP账号 ${ipId} 更新账号定位，视频总数量: ${videoList.length}`);

    try {
      // 先筛选出文本长度在20到1000之间的视频
      const filteredVideos = videoList.filter(video => {
        if (!video.audioText) return false;
        const length = video.audioText.length;
        return length > 20 && length <= 5000;
      });

      if (filteredVideos.length === 0) {
        this.logger.warn(`IP账号 ${ipId} 没有符合条件的视频数据（文本长度20-5000字之间）`);
        return {
          success: false,
          message: '没有找到符合条件的视频数据（文本长度需在20-5000字之间），无法进行账号定位',
          data: { total: 0 },
        };
      }

      this.logger.log(`符合文本长度条件（20-5000字）的视频数量: ${filteredVideos.length}/${videoList.length}`);

      // 选择createTime最近的20条视频数据进行分析
      let sampledVideoList = filteredVideos;
      if (filteredVideos.length > 20) {
        // 按createTime降序排序并取前20个元素
        sampledVideoList = [...filteredVideos]
          .sort((a, b) => {
            // 如果createTime存在则按createTime排序，否则按createdAt排序
            const timeA = a.createTime ? new Date(a.createTime).getTime() : a.createdAt.getTime();
            const timeB = b.createTime ? new Date(b.createTime).getTime() : b.createdAt.getTime();
            return timeB - timeA; // 降序排序，最新的在前
          })
          .slice(0, 20);
        this.logger.log(`从${filteredVideos.length}条符合条件的视频中选择了createTime最近的20条数据用于账号定位分析`);
      }

      // 3. 调用Coze工作流分析账号定位
      const result = await this.cozeWorkflowManager.analyzeAccountPositioning({
        ip_id: ipAccount,
        videos: sampledVideoList,
      });

      if (!result) {
        return {
          success: false,
          message: '账号定位分析未返回结果',
          data: { total: 0 },
        };
      }

      // 4. 更新IP账号的定位信息
      if (result) {
        await this.ipService.update(ipId, {
          benchmarkingAnalysis: result,
        });

        return {
          success: true,
          message: '账号定位更新成功',
          data: {
            total: 1,
            updated: 1,
            positioning: result,
          },
        };
      } else {
        return {
          success: false,
          message: '账号定位分析结果不完整',
          data: {
            total: 0,
            positioning: null,
          },
        };
      }
    } catch (error) {
      this.logger.error(`账号定位分析出错: ${error.message}`);
      return {
        success: false,
        message: `账号定位分析失败: ${error.message}`,
        data: { total: 0 },
      };
    }
  }

  /**
   * 手动为单个视频生成文案
   * @param params 包含视频ID和可选的音频URL
   * @returns 生成结果
   */
  async generateAudioText(params: { id: string; audioUrl?: string }): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    try {
      // 1. 查找视频记录
      const video = await this.benchmarkingRepository.findOne({
        where: { id: params.id },
      });

      if (!video) {
        return {
          success: false,
          message: `找不到ID为 ${params.id} 的视频记录`,
        };
      }

      // 2. 设置状态为正在生成
      video.audioText = '生成中...';
      await this.benchmarkingRepository.save(video);

      // 3. 获取音频URL
      const audioUrl = params.audioUrl || video.audioUrl;

      if (!audioUrl) {
        video.audioText = '无法获取音频URL';
        await this.benchmarkingRepository.save(video);

        return {
          success: false,
          message: '无法获取音频URL，无法生成文案',
        };
      }

      // 4. 调用Coze工作流生成文案
      try {
        this.logger.log(`开始为视频 ${video.id} 生成文案，音频URL: ${audioUrl}`);

        // 异步调用工作流并更新数据库
        this.generateSingleAudioTextAsync(video, audioUrl);

        return {
          success: true,
          message: '文案生成请求已提交，请稍后查看结果',
          data: { id: video.id, status: '处理中' },
        };
      } catch (workflowError) {
        this.logger.error(`调用工作流生成文案失败: ${workflowError.message}`);

        // 更新状态为失败
        video.audioText = '文案生成失败';
        await this.benchmarkingRepository.save(video);

        return {
          success: false,
          message: `调用工作流生成文案失败: ${workflowError.message}`,
        };
      }
    } catch (error) {
      this.logger.error(`生成视频文案失败: ${error.message}`, error.stack);
      return {
        success: false,
        message: `生成视频文案失败: ${error.message}`,
      };
    }
  }

  /**
   * 异步处理视频文案生成
   * @param items 需要处理的视频项目
   * 注意：此方法仅处理文案生成，不会触发账号定位更新
   */
  private async processAudioTextAsync(items: Array<{ item: any, record: Benchmarking }>): Promise<void> {
    try {
      // 提取音频URL
      const audioUrls = items.map(item => item.item.audioUrl).filter(Boolean);

      if (audioUrls.length === 0) {
        this.logger.warn('没有有效的音频URL可以处理');
        return;
      }

      this.logger.log(`开始处理 ${audioUrls.length} 个音频URL的文案生成（仅更新视频数据，不更新账号定位）`);

      // 为每个音频URL异步调用工作流（一个接一个处理）
      const workflowId = '7511265128399749131';

      // 逐个处理每个URL，避免一次性发送过多请求
      for (let i = 0; i < audioUrls.length; i++) {
        const audioUrl = audioUrls[i];
        const record = items.find(item => item.item.audioUrl === audioUrl)?.record;

        if (!record) continue;

        try {
          // 设置状态为处理中
          await this.benchmarkingRepository.update(
            { id: record.id },
            { audioText: '文案生成中...' }
          );

          // 调用单个音频处理
          await this.generateSingleAudioTextAsync(record, audioUrl);

          // 添加小延迟，避免API请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (itemError) {
          this.logger.error(`处理视频 ${record.id} 文案失败: ${itemError.message}`);
        }
      }

      this.logger.log('所有文案生成处理完成');
    } catch (error) {
      this.logger.error(`异步处理文案生成失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 异步处理单个视频的文案生成
   * @param video 视频记录
   * @param audioUrl 音频URL
   */
  private async generateSingleAudioTextAsync(video: Benchmarking, audioUrl: string): Promise<void> {
    try {
      // 调用Coze工作流
      const audioText = await this.cozeWorkflowManager.extractAudioText({
        input: audioUrl, // 直接传递audioUrl作为input参数
      }, true); // 使用备用工作流

      // 更新数据库
      await this.benchmarkingRepository.update(
        { id: video.id },
        { audioText },
      );

      this.logger.log(`视频 ${video.id} 的文案已更新`);
    } catch (error) {
      this.logger.error(`为视频 ${video.id} 生成文案失败: ${error.message}`);

      // 更新为失败状态
      try {
        await this.benchmarkingRepository.update(
          { id: video.id },
          { audioText: '文案生成失败' },
        );
      } catch (updateError) {
        this.logger.error(`更新视频 ${video.id} 状态失败: ${updateError.message}`);
      }
    }
  }

  /**
   * 获取IP账号详情
   * @param ipId IP账号ID
   * @returns IP账号详细信息
   */
  async getIpAccountDetails(ipId: string): Promise<any> {
    try {
      return await this.ipService.findOne(ipId);
    } catch (error) {
      this.logger.error(`获取IP账号详情失败: ${error.message}`, error.stack);
      throw new Error(`获取IP账号详情失败: ${error.message}`);
    }
  }
}
