// src/modules/coze/dto/req/material.req.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from "class-validator";
import { Column } from 'typeorm';
import { ZtBaseReqDto } from '../../../../../utils/baseReq.dto';

export class MaterialReqDto extends ZtBaseReqDto{
  /**
   * 图片URL
   */
  @ApiProperty({ example: 'https://example.com/image.jpg', description: '图片URL' })
  @Column({length: 2000})
  @IsNotEmpty()
  @IsString()
  imgUrl: string;

  /**
   * 图片描述
   */
  @ApiProperty({ example: '产品展示图', description: '图片描述' })
  @Column("text")
  @IsNotEmpty()
  @IsString()
  imgDesc: string;

  /**
   * 类型
   */
  @ApiProperty({ example: '产品图', description: '类型' })
  @Column()
  @IsNotEmpty()
  @IsString()
  type: string;

  /**
   * 商户ID
   */
  @ApiProperty({ example: 'MERCHANT001', description: '商户id' })
  @Column()
  @IsNotEmpty()
  @IsString()
  merchantId: string;
}