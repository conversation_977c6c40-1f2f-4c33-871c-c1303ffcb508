import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsArray, ValidateNested, IsString } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { Material } from '../../entities/material.entity';

/**
 * 批量创建素材DTO
 */
export class BatchCreateMaterialDto {
  /**
   * 素材列表 - 支持数组或JSON字符串格式
   */
  @ApiProperty({
    description: '素材列表，支持数组或JSON字符串格式',
    example: [
      {
        imgUrl: 'https://example.com/image1.jpg',
        imgDesc: '产品展示图1',
        type: '产品图',
        merchantId: 'MERCHANT001'
      },
      {
        imgUrl: 'https://example.com/image2.jpg',
        imgDesc: '产品展示图2',
        type: '产品图',
        merchantId: 'MERCHANT001'
      }
    ],
    oneOf: [
      { type: 'array', items: { $ref: '#/components/schemas/Material' } },
      { type: 'string', description: 'JSON字符串格式的素材数组' }
    ]
  })
  @IsNotEmpty({ message: 'materials不能为空' })
  materials: Material[] | string;
} 