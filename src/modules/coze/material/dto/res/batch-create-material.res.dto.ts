import { ApiProperty } from '@nestjs/swagger';

/**
 * 批量创建素材响应数据
 */
export class BatchCreateMaterialDataDto {
  @ApiProperty({ description: '成功创建的数量' })
  successCount: number;

  @ApiProperty({ description: '创建失败的数量' })
  failedCount: number;

  @ApiProperty({ description: '总数量' })
  totalCount: number;

  @ApiProperty({ description: '错误信息列表', type: [String] })
  errors: string[];
}

/**
 * 批量创建素材响应DTO
 */
export class BatchCreateMaterialResDto {
  @ApiProperty({ description: '操作是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiProperty({ description: '响应数据', type: BatchCreateMaterialDataDto, nullable: true })
  data: BatchCreateMaterialDataDto | null;
} 