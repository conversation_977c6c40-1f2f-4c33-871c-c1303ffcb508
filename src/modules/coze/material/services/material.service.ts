import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CommonService } from '../../../../utils/common.service';
import { Material } from '../entities/material.entity';
import { RemoveReqDto } from 'src/utils/remove.req.dto';
import { ZtBaseResDto } from 'src/utils/baseRes.dto';
import { ProjectResDto } from '../../project/dto/res/project.res.dto';
import { CozeWorkflowManager } from '../../coze-workflow.manager';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { reqUser } from '../../../../utils/nameSpace';

@Injectable()
export class MaterialService extends CommonService {
  constructor(
    @InjectRepository(Material)
    private readonly materialRepository: Repository<Material>,
    @Inject(REQUEST) request: Request,
    private cozeWorkflowManager: CozeWorkflowManager,
  ) {
    super(request);
  }

  // Material CRUD
  async createMaterial(material: Material): Promise<void> {
    return this.createEntity(this.materialRepository, material, 'Material');
  }

  async createBatchMaterials(materials: Material[]): Promise<{
    successCount: number;
    failedCount: number;
    errors: string[];
  }> {
    const errors: string[] = [];
    let successCount = 0;
    let failedCount = 0;

    // 使用事务确保数据一致性
    await this.materialRepository.manager.transaction(async transactionalEntityManager => {
      for (let i = 0; i < materials.length; i++) {
        try {
          const material = materials[i];
          
          // 验证必填字段
          if (!material.imgUrl || !material.imgDesc || !material.merchantId) {
            throw new Error(`第${i + 1}个素材缺少必填字段`);
          }

          // 检查URL是否已存在
          const existingMaterial = await transactionalEntityManager.findOne(Material, {
            where: { imgUrl: material.imgUrl, merchantId: material.merchantId }
          });

          if (existingMaterial) {
            errors.push(`第${i + 1}个素材的图片URL已存在: ${material.imgUrl}`);
            failedCount++;
            continue;
          }

          // 设置默认值
          if (!material.type) {
            material.type = '产品图';
          }

          // 创建素材
          await transactionalEntityManager.save(Material, material);
          successCount++;
        } catch (error) {
          errors.push(`第${i + 1}个素材创建失败: ${error.message}`);
          failedCount++;
        }
      }
    });

    return {
      successCount,
      failedCount,
      errors
    };
  }

  async updateMaterial(id: string, material: Partial<Material>): Promise<void> {
    return this.updateEntity(this.materialRepository, id, material, 'Material');
  }

  async deleteMaterial(params: RemoveReqDto): Promise<void> {
    return this.deleteEntity(this.materialRepository, params, 'Material');
  }

  async findMaterial(filter: Partial<Material>): Promise<ZtBaseResDto> {
    return this.findWithPagination(
      this.materialRepository,
      filter,
      ProjectResDto,
    );
  }

  async syncData() {
    const res = await this.cozeWorkflowManager.processMaterial();
    console.log(6666, res);
    for (const i of res) {
      const hasImg = await this.materialRepository.findOne({
        where: {
          imgUrl: i.img_url,
        },
      });
      const material = new Material();
      material.imgUrl = i.img_url;
      material.imgDesc = i.img_desc;
      material.merchantId = i.merchant_id;
      material.type = i.type;
      if (!hasImg) {
        await this.createMaterial(material);
      } else {
        await this.updateMaterial(hasImg.id, material);
      }
    }
    return {
      message: '同步成功',
    };
  }
}
