import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { MaterialService } from '../services/material.service';
import { Material } from '../entities/material.entity';
import { MaterialReqDto } from '../dto/req/material.req.dto';
import { RemoveReqDto } from '../../../../utils/remove.req.dto';

interface BatchCreateDto {
  materials: Material[] | string;
}

@ApiTags('coze')
@Controller('coze/material')
export class MaterialController {
  constructor(private readonly materialService: MaterialService) {}

  // Material CRUD
  @Post('create')
  @ApiOperation({ summary: '创建素材' })
  async createMaterial(@Body() material: Material) {
    return this.materialService.createMaterial(material);
  }

  @Post('createBatch')
  @ApiOperation({ summary: '批量创建素材' })
  async createBatchMaterials(@Body() data: BatchCreateDto) {
    let materials: Material[] = data.materials as Material[];
    if (typeof data.materials === 'string') {
      materials = JSON.parse(data.materials);
    }
    return this.materialService.createBatchMaterials(materials);
  }

  @Post('update')
  @ApiOperation({ summary: '更新素材' })
  async updateMaterial(@Body() material: MaterialReqDto) {
    return this.materialService.updateMaterial(material.id, material);
  }

  @Post('remove')
  @ApiOperation({ summary: '删除素材' })
  async deleteMaterial(@Body() params: RemoveReqDto) {
    return this.materialService.deleteMaterial(params);
  }

  @Post('find')
  @ApiOperation({ summary: '查询素材' })
  async findMaterial(@Body() filter: Partial<Material>) {
    return this.materialService.findMaterial(filter);
  }

  @Post('syncData')
  @ApiOperation({ summary: '同步素材' })
  async syncData() {
    return this.materialService.syncData();
  }
}
