import { Column, Entity } from 'typeorm';
import { ZtBaseEntity } from '../../../../utils/base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

/**
 * 素材实体
 */
@Entity()
export class Material extends ZtBaseEntity {
  /**
   * 图片URL
   */
  @ApiProperty({
    example: 'https://example.com/image.jpg',
    description: '图片URL',
  })
  @Column({ length: 2000 })
  @IsNotEmpty()
  imgUrl: string;

  /**
   * 图片描述
   */
  @ApiProperty({ example: '产品展示图', description: '图片描述' })
  @Column('text')
  @IsNotEmpty()
  imgDesc: string;

  /**
   * 类型
   */
  @ApiProperty({ example: '产品图', description: '类型' })
  @Column({ default: '产品图' })  // 添加默认值
  @IsNotEmpty()
  type: string;

  /**
   * 商户ID
   */
  @ApiProperty({ example: 'MERCHANT001', description: '商户id' })
  @Column()
  @IsNotEmpty()
  merchantId: string;
}
