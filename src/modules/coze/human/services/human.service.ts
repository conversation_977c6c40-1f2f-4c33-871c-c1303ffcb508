import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CommonService } from '../../../../utils/common.service';
import { Human } from '../entities/human.entity';
import { HumanCreateDto } from '../dto/req/human.create.dto';
import { RemoveReqDto } from 'src/utils/remove.req.dto';
import { ZtBaseResDto } from 'src/utils/baseRes.dto';
import { HumanResDto } from '../dto/res/human.res.dto';
import { CozeWorkflowManager } from '../../coze-workflow.manager';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { reqUser } from '../../../../utils/nameSpace';

@Injectable()
export class HumanService extends CommonService {
  constructor(
    @InjectRepository(Human)
    private readonly humanRepository: Repository<Human>,
    @Inject(REQUEST) request: Request,
    private cozeWorkflowManager: CozeWorkflowManager,
  ) {
    super(request);
  }

  // Human CRUD
  async createHuman(human: HumanCreateDto): Promise<void> {
    return this.createEntity(this.humanRepository, human, 'Human');
  }

  async updateHuman(id: string, human: Partial<Human>): Promise<void> {
    return this.updateEntity(this.humanRepository, id, human, 'Human');
  }

  async deleteHuman(params: RemoveReqDto): Promise<void> {
    return this.deleteEntity(this.humanRepository, params, 'Human');
  }

  async findHuman(filter: Partial<Human>): Promise<ZtBaseResDto> {
    return this.findWithPagination(this.humanRepository, filter, HumanResDto);
  }

  async syncData() {
    const res = await this.cozeWorkflowManager.processHuman();
    for (const i of res) {
      const hasImg = await this.humanRepository.findOne({
        where: {
          humanVideoUrl: i.human_video_url,
        },
      });
      const human = new Human();
      human.humanVideoUrl = i.human_video_url;
      human.description = i.description;
      human.merchantId = i.merchant_id;
      if (!hasImg) {
        await this.createHuman(human);
      } else {
        await this.updateHuman(hasImg.id, human);
      }
    }
    return {
      message: '同步成功',
    };
  }

  async findAll(param: { merchantId: string }) {
    const { merchantId } = param;
    return this.humanRepository.find({
      where: {
        merchantId,
      },
    });
  }
}
