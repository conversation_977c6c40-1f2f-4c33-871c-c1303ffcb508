import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { HumanService } from '../services/human.service';
import { HumanCreateDto } from '../dto/req/human.create.dto';
import { HumanReqDto } from '../dto/req/human.req.dto';
import { RemoveReqDto } from 'src/utils/remove.req.dto';
import { Human } from '../entities/human.entity';

@ApiTags('coze/human')
@Controller('coze/human')
export class HumanController {
  constructor(private readonly humanService: HumanService) {}

  // Human CRUD
  @Post('create')
  @ApiOperation({ summary: '创建数字人' })
  async createHuman(@Body() human: HumanCreateDto) {
    return this.humanService.createHuman(human);
  }

  @Post('update')
  @ApiOperation({ summary: '更新数字人' })
  async updateHuman(@Body() human: HumanReqDto) {
    return this.humanService.updateHuman(human.id, human);
  }

  @Post('remove')
  @ApiOperation({ summary: '删除数字人' })
  async deleteHuman(@Body() params: RemoveReqDto) {
    return this.humanService.deleteHuman(params);
  }

  @Post('find')
  @ApiOperation({ summary: '查询数字人' })
  async findHuman(@Body() filter: Partial<Human>) {
    return this.humanService.findHuman(filter);
  }

  @Post('syncData')
  @ApiOperation({ summary: '同步数字人' })
  async syncData() {
    return this.humanService.syncData();
  }
}
