import { Column, Entity } from 'typeorm';
import { ZtBaseEntity } from '../../../../utils/base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

/**
 * 数字人实体
 */
@Entity()
export class Human extends ZtBaseEntity {
  /**
   * 数字人素材
   */
  @ApiProperty({
    example: 'https://example.com/video.mp4',
    description: '数字人素材',
  })
  @Column({ nullable: true })
  humanVideoUrl: string;

  /**
   * 数字人素材描述
   */
  @ApiProperty({ example: '专业主播数字人', description: '数字人素材描述' })
  @Column('text')
  description: string;

  /**
   * 商户ID
   */
  @ApiProperty({ example: 'MERCHANT001', description: '商户id' })
  @Column()
  @IsNotEmpty()
  merchantId: string;
}
