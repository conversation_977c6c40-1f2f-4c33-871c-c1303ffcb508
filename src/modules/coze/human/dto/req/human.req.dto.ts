// src/modules/coze/dto/req/human.req.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { Column } from 'typeorm';
import { ZtBaseReqDto } from '../../../../../utils/baseReq.dto';

export class HumanReqDto extends ZtBaseReqDto {
  /**
   * 数字人素材
   */
  @ApiProperty({
    example: 'https://example.com/video.mp4',
    description: '数字人素材',
  })
  @Column({ nullable: true })
  humanVideoUrl?: string;

  /**
   * 数字人素材描述
   */
  @ApiProperty({ example: '专业主播数字人', description: '数字人素材描述' })
  @Column()
  description: string;

  /**
   * 商户ID
   */
  @ApiProperty({ example: 'MERCHANT001', description: '商户id' })
  @Column()
  @IsNotEmpty()
  merchantId: string;
}
