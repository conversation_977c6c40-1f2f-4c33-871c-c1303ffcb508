import { Entity, Column } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';

/**
 * 数字人实体
 */
@Entity()
export class HumanCreateDto {
  /**
   * 数字人素材
   */
  @ApiProperty({
    example: 'https://example.com/video.mp4',
    description: '数字人素材',
  })
  @IsOptional()
  humanVideoUrl: string;

  /**
   * 数字人素材描述
   */
  @ApiProperty({ example: '专业主播数字人', description: '数字人素材描述' })
  @IsOptional()
  description: string;

  /**
   * 商户ID
   */
  @ApiProperty({ example: 'MERCHANT001', description: '商户id' })
  @Column()
  @IsNotEmpty()
  merchantId: string;
}
