# Coze工作流管理器使用指南

本文档介绍如何使用统一的Coze工作流管理器，提供类型安全和集中管理的工作流调用方式。

## 快速开始

### 1. 导入管理器

```typescript
import { CozeWorkflowManager, CozeWorkflowId } from '../coze-workflow.manager';

@Injectable()
export class YourService {
  constructor(
    private readonly cozeWorkflowManager: CozeWorkflowManager,
  ) {}
}
```

### 2. 基本使用

```typescript
// 生成视频内容
const videoContent = await this.cozeWorkflowManager.generateVideoContent({
  account_positioning: '科技博主',
  knowledge: '人工智能技术',
  qa: [],
  project_name: '我的项目',
  human: [],
  benchmarking_list: [],
  hot: {},
  limit: '10',
  date: '2024-01-01'
});

// 提取音频文案
const audioText = await this.cozeWorkflowManager.extractAudioText({
  input: 'https://example.com/audio.mp3'
});
```

## 可用的工作流方法

### 视频相关

#### 生成视频内容
```typescript
generateVideoContent(params: VideoContentParams): Promise<any[]>
```

**参数说明：**
- `account_positioning`: 账号定位
- `knowledge`: 知识库内容
- `qa`: 问答数据数组
- `project_name`: 项目名称
- `human`: 人物数据数组
- `benchmarking_list`: 基准数据列表
- `hot`: 热点数据
- `limit`: 生成数量限制
- `date`: 日期

**使用示例：**
```typescript
const result = await this.cozeWorkflowManager.generateVideoContent({
  account_positioning: '美食博主',
  knowledge: '川菜制作技巧',
  qa: [{ question: '如何做麻婆豆腐', answer: '...' }],
  project_name: '美食分享',
  human: [{ id: '1', name: '主播小明' }],
  benchmarking_list: [],
  hot: { title: '今日热门菜谱' },
  limit: '5',
  date: '2024-01-15'
});
```

#### 生成视频文件
```typescript
generateVideoFile(params: VideoFileParams): Promise<string>
```

**参数说明：**
- `segment`: 分镜头数据数组
- `merchant_id`: 商户ID
- `human_id`: 人物ID

### 内容获取

#### 获取热点内容
```typescript
fetchHotContent(params: HotContentParams): Promise<any[]>
```

**参数说明：**
- `merchant_id`: 商户ID
- `input`: 输入文本
- `date`: 日期

### 商户数据管理

#### 创建商户数据
```typescript
createMerchantData(params: MerchantDataParams): Promise<any>
```

#### 编辑商户数据
```typescript
editMerchantData(params: MerchantDataParams): Promise<any>
```

#### 删除商户数据
```typescript
deleteMerchantData(merchantId: string): Promise<any>
```

### 音频处理

#### 提取音频文案
```typescript
extractAudioText(params: AudioTextParams, useAlternative?: boolean): Promise<string>
```

**参数说明：**
- `input`: 音频输入URL或其他格式
- `useAlternative`: 是否使用备用工作流（可选，默认false）

**使用示例：**
```typescript
// 使用默认工作流
const text1 = await this.cozeWorkflowManager.extractAudioText({
  input: 'https://example.com/audio.mp3'
});

// 使用备用工作流
const text2 = await this.cozeWorkflowManager.extractAudioText({
  input: 'https://example.com/audio.mp3'
}, true);
```

### 其他处理

#### 处理材料
```typescript
processMaterial(): Promise<any>
```

#### 处理问答
```typescript
processQA(params: WorkflowParams): Promise<any>
```

#### 处理人物
```typescript
processHuman(): Promise<any>
```

#### 分析账号定位
```typescript
analyzeAccountPositioning(params: AccountPositioningParams): Promise<any>
```

## 自定义工作流

对于未预定义的工作流或新增的工作流，可以使用自定义工作流方法：

```typescript
executeCustomWorkflow(
  workflowId: string,
  params: WorkflowParams,
  description?: string
): Promise<any>
```

**使用示例：**
```typescript
const result = await this.cozeWorkflowManager.executeCustomWorkflow(
  '7512345678901234567', // 新的工作流ID
  {
    custom_param: 'value',
    another_param: 123
  },
  '自定义业务流程'
);
```

## 添加新工作流

### 1. 更新枚举

在 `CozeWorkflowId` 枚举中添加新的工作流ID：

```typescript
export enum CozeWorkflowId {
  // ... 现有的工作流
  
  /** 新的工作流 */
  NEW_WORKFLOW = '7512345678901234567',
}
```

### 2. 定义参数接口（可选）

```typescript
export interface NewWorkflowParams extends WorkflowParams {
  /** 参数1 */
  param1: string;
  /** 参数2 */
  param2: number;
}
```

### 3. 添加方法

```typescript
/**
 * 新的工作流处理
 * @param params 新工作流参数
 * @returns 处理结果
 */
async processNewWorkflow(params: NewWorkflowParams): Promise<any> {
  return this.executeWorkflow(
    CozeWorkflowId.NEW_WORKFLOW,
    params,
    '新工作流处理',
  );
}
```

## 错误处理

工作流管理器提供了统一的错误处理和日志记录：

```typescript
try {
  const result = await this.cozeWorkflowManager.generateVideoContent(params);
  // 处理成功结果
} catch (error) {
  // 工作流执行失败
  this.logger.error('工作流执行失败', error);
  throw error;
}
```

## 最佳实践

1. **使用类型安全的接口**：尽量使用预定义的参数接口，避免使用通用的 `WorkflowParams`
2. **添加描述信息**：在调用自定义工作流时，提供有意义的描述信息
3. **错误处理**：始终包装工作流调用在 try-catch 块中
4. **日志记录**：管理器已内置日志，但可以在业务层添加额外的日志记录
5. **参数验证**：在调用工作流前验证必要的参数

## 迁移指南

### 从直接调用 runWorkflow 迁移

**原有代码：**
```typescript
import { runWorkflow } from '../../../utils/common';

const result = await runWorkflow('7511339947418746892', {
  account_positioning: 'value',
  // ... 其他参数
});
```

**迁移后：**
```typescript
import { CozeWorkflowManager } from '../coze-workflow.manager';

constructor(
  private readonly cozeWorkflowManager: CozeWorkflowManager,
) {}

const result = await this.cozeWorkflowManager.generateVideoContent({
  account_positioning: 'value',
  // ... 其他参数
});
```

### 好处

1. **类型安全**：TypeScript 类型检查防止参数错误
2. **集中管理**：所有工作流ID和配置在一个地方
3. **易于维护**：修改工作流ID只需要在一个地方
4. **统一日志**：所有工作流调用都有统一的日志格式
5. **错误处理**：统一的错误处理机制
6. **可扩展性**：容易添加新的工作流和功能