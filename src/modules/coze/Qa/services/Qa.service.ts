import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CommonService } from '../../../../utils/common.service';
import { RemoveReqDto } from 'src/utils/remove.req.dto';
import { ZtBaseResDto } from '../../../../utils/baseRes.dto';
import { Qa } from '../entities/qa.entity';
import { QaResDto } from '../dto/res/qa.res.dto';
import { CozeWorkflowManager } from '../../coze-workflow.manager';
import { QaGetDataReqDto } from '../dto/req/qa.getData.req.dto';

import { ProjectService } from '../../project/services/project.service';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { reqUser } from '../../../../utils/nameSpace';

@Injectable()
export class QaService extends CommonService {
  constructor(
    @InjectRepository(Qa)
    private qaRepository: Repository<Qa>,
    private projectService: ProjectService,
    @Inject(REQUEST) request: Request,
    private cozeWorkflowManager: CozeWorkflowManager,
  ) {
    super(request);
  }

  // QA CRUD
  async createQa(qa: Qa | Qa[]): Promise<void> {
    return this.createEntity(this.qaRepository, qa, 'QA');
  }

  async updateQa(id: string, qa: Partial<Qa>): Promise<void> {
    return this.updateEntity(this.qaRepository, id, qa, 'QA');
  }

  async deleteQa(params: RemoveReqDto): Promise<void> {
    return this.deleteEntity(this.qaRepository, params, 'QA');
  }

  async findQa(filter: Partial<Qa>): Promise<ZtBaseResDto> {
    return this.findWithPagination(this.qaRepository, filter, QaResDto);
  }

  async qaGetData(cozeDto: QaGetDataReqDto) {
    const project = await this.projectService.findOne(cozeDto.merchantId);

    //获取QA数据
    const qas = await this.findAll({ merchantId: project.id });

    const res: any = await this.cozeWorkflowManager.processQA({
      knowledge: project.knowledge,
      projectName: project.projectName,
      limit: cozeDto.limit,
      qas,
    });

    console.log(77788, res);
    const arr: Qa[] = [];
    for (const key of res) {
      const qaObj = new Qa();
      qaObj.userQuestions = key['userQuestions'];
      qaObj.standardAnswerMarket = key['standardAnswerMarket'];
      qaObj.merchantId = cozeDto.merchantId;
      arr.push(qaObj);
    }
    // if (res.length > 0) {
    //   await this.deleteQaByMerchantId(cozeDto.merchantId);
    // }
    await this.createQa(arr);
    return res;
  }

  async deleteQaByMerchantId(merchantId: string) {
    await this.qaRepository.delete({ merchantId });
  }

  async findAll(param: { merchantId: string }) {
    return this.qaRepository.find({
      where: {
        merchantId: param.merchantId,
      },
    });
  }
}
