// src/modules/coze/dto/req/qa.req.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from "class-validator";
import { Column } from 'typeorm';
import { ZtBaseReqDto } from '../../../../../utils/baseReq.dto';

export class QaReqDto extends ZtBaseReqDto{
  /**
   * 问题内容
   */
  @ApiProperty({ 
    example: '你们家的炭为什么点火这么慢？,有没有燃烧时间更长的炭推荐？', 
    description: '问题内容' 
  })
  @Column()
  @IsNotEmpty()
  @IsString()
  userQuestions: string;

  /**
   * 标准回答
   */
  @ApiProperty({ 
    example: '亲~我们独家研发的『三秒速燃菊炭』...', 
    description: '标准回答' 
  })
  @Column()
  @IsNotEmpty()
  @IsString()
  standardAnswerMarket: string;

  /**
   * 项目ID
   */
  @ApiProperty({ example: 'PROJECT001', description: '项目id' })
  @Column()
  @IsNotEmpty()
  @IsString()
  merchantId: string;
}