// src/modules/coze/Qa/dto/req/qa.getData.req.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Column } from 'typeorm';
import { ZtBaseReqDto } from '../../../../../utils/baseReq.dto';
import { CozeReqDto } from '../../../benchmarking/dto/req/coze.req.dto';

export class QaGetDataReqDto extends CozeReqDto {
  /**
   * 获取条数
   */
  @ApiProperty({ example: '10', description: '获取条数' })
  @IsOptional()
  @IsString()
  limit: string;

  /**
   * 商户ID
   */
  @ApiProperty({ example: 'MERCHANT001', description: '商户id' })
  @IsOptional()
  @IsString()
  merchantId: string;
}
