import { Column, Entity } from 'typeorm';
import { ZtBaseEntity } from '../../../../utils/base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

@Entity()
export class Qa extends ZtBaseEntity {
  @ApiProperty({
    example: '你们家的炭为什么点火这么慢？,有没有燃烧时间更长的炭推荐？',
    description: '问题内容',
  })
  @Column({ length: 1000 })
  userQuestions: string;

  @ApiProperty({
    example: '亲~我们独家研发的『三秒速燃菊炭』...',
    description: '标准回答',
  })
  @Column({ length: 5000 })
  @IsNotEmpty()
  standardAnswerMarket: string;

  @ApiProperty({ example: 'MERCHANT001', description: '商户id' })
  @Column()
  @IsNotEmpty()
  merchantId: string;

  /**
   * 用户项目ID列表（用于项目权限过滤）
   * 存储为JSON字符串，包含用户有权限访问的项目ID列表
   */
  @ApiProperty({
    example: '["project1", "project2"]',
    description: '用户项目ID列表，用于权限过滤',
    required: false
  })
  @Column('text', { nullable: true })
  _userProjectIds?: string;
}
