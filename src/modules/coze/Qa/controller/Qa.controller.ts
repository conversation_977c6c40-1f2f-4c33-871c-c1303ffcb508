import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RemoveReqDto } from '../../../../utils/remove.req.dto';
import { SkipAuth } from '../../../../decorators/skip-auth.decorator';
import { Qa } from '../entities/qa.entity';
import { QaReqDto } from '../dto/req/qa.req.dto';
import { QaService } from '../services/Qa.service';
import { QaGetDataReqDto } from '../dto/req/qa.getData.req.dto';

@ApiTags('coze-qa')
@Controller('coze/qa')
export class QaController {
  constructor(private readonly qaService: QaService) {}

  @Post('create')
  @ApiOperation({ summary: '创建问答' })
  @ApiResponse({ status: 201, description: '问答创建成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  async createQa(@Body() qa: Qa) {
    return this.qaService.createQa(qa);
  }

  @Post('update')
  @ApiOperation({ summary: '更新问答' })
  @ApiResponse({ status: 200, description: '问答更新成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  @ApiResponse({ status: 404, description: '问答不存在' })
  async updateQa(@Body() qa: QaReqDto) {
    return this.qaService.updateQa(qa.id, qa);
  }

  @Post('remove')
  @ApiOperation({ summary: '删除问答' })
  @ApiResponse({ status: 200, description: '问答删除成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  @ApiResponse({ status: 404, description: '问答不存在' })
  async deleteQa(@Body() params: RemoveReqDto) {
    return this.qaService.deleteQa(params);
  }

  @Post('find')
  @ApiOperation({ summary: '查询问答' })
  @ApiResponse({ status: 200, description: '问答查询成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  async findQa(@Body() filter: Partial<Qa>) {
    return this.qaService.findQa(filter);
  }

  @Post('getData')
  @SkipAuth()
  @ApiOperation({ summary: '获取问答数据' })
  @ApiResponse({ status: 200, description: '获取问答数据成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  async qaGetData(@Body() cozeDto: QaGetDataReqDto) {
    return this.qaService.qaGetData(cozeDto);
  }
}
