import {
  Body,
  Controller,
  HttpException,
  HttpStatus,
  Param,
  Post,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { IpService } from '../services/ip.service';
import { CreateIpDto } from '../dto';
import { RemoveReqDto } from '../../../../utils/remove.req.dto';
import { IpFindDtoRes } from '../dto';
import { IpFindDto } from '../dto';
import { IpEntity } from '../entities/ip.entity';
import { SimpleCreateIpDto } from '../dto/req/ip.simple.create.dto';
import { SimpleCreateIpResDto } from '../dto/res/ip.simple.create.res.dto';

@ApiTags('coze-ip')
@Controller('coze/ip')
export class IpController {
  constructor(private readonly ipService: IpService) {}

  @Post('create')
  @ApiOperation({ summary: '创建IP' })
  @ApiResponse({ status: 201, description: 'IP创建成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  async createIp(@Body() ip: CreateIpDto) {
    try {
      return await this.ipService.createIp(ip);
    } catch (error) {
      throw new HttpException(
        `创建IP失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
  
  @Post('simple-create')
  @ApiOperation({ summary: '简化创建IP并获取视频' })
  @ApiResponse({ status: 201, description: 'IP创建并获取视频成功', type: SimpleCreateIpResDto })
  @ApiResponse({ status: 400, description: '创建失败' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async simpleCreateIp(@Body() data: SimpleCreateIpDto) {
    try {
      const result = await this.ipService.simpleCreateIpWithVideos(data);
      
      // 创建响应数据对象
      const responseData: any = {
        ip: result.data.ip,
        videos: result.data.videos
      };
      
      // 如果存在定位数据，则添加到响应中
      if (result.data.positioning) {
        responseData.positioning = result.data.positioning;
      }
      
      // 返回正确格式的响应对象
      return {
        code: 200,
        data: responseData,
        msg: result.message
      };
    } catch (error) {
      throw new HttpException(
        {
          code: -1,
          msg: `简化创建IP失败: ${error.message}`
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('update')
  @ApiOperation({ summary: '更新IP' })
  @ApiResponse({ status: 200, description: 'IP更新成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  @ApiResponse({ status: 404, description: 'IP不存在' })
  async updateIp(@Body() ip: Partial<CreateIpDto>) {
    try {
      await this.ipService.updateIpWithProjects(ip);
      return { message: '更新成功' };
    } catch (error) {
      throw new HttpException(
        error.message || '更新IP失败',
        error.status || HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('refresh/:id')
  @ApiOperation({ summary: '从抖音刷新IP数据' })
  @ApiResponse({ status: 200, description: 'IP数据刷新成功', type: IpEntity })
  @ApiResponse({ status: 400, description: '刷新失败' })
  @ApiResponse({ status: 404, description: 'IP不存在' })
  async refreshIp(@Param('id') id: string) {
    try {
      const refreshedIp = await this.ipService.refreshIpData(id);
      return {
        message: 'IP数据刷新成功',
        data: refreshedIp,
      };
    } catch (error) {
      throw new HttpException(
        `刷新IP数据失败: ${error.message}`,
        error.status || HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('remove')
  @ApiOperation({ summary: '删除IP' })
  @ApiResponse({ status: 200, description: 'IP删除成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  @ApiResponse({ status: 404, description: 'IP不存在' })
  async deleteIp(@Body() params: RemoveReqDto) {
    try {
      return await this.ipService.delete(params);
    } catch (error) {
      throw new HttpException(
        `删除IP失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('find')
  @ApiOperation({ summary: '查询IP' })
  @ApiResponse({ status: 200, description: '查询成功', type: IpFindDtoRes })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  async findIp(@Body() filter: IpFindDto) {
    try {
      return await this.ipService.find(filter);
    } catch (error) {
      throw new HttpException(
        `查询IP失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('findAll')
  @ApiOperation({ summary: '查询所有IP' })
  @ApiResponse({ status: 200, description: '查询成功', type: [IpFindDtoRes] })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  async findAllIp(@Body() filter: Partial<IpFindDtoRes> = {}) {
    try {
      return await this.ipService.findAll(filter);
    } catch (error) {
      throw new HttpException(
        `查询所有IP失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
