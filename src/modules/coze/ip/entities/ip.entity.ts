import { Column, Entity, JoinColumn, ManyToMany } from 'typeorm';
import { ZtBaseEntity } from '../../../../utils/base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { Project } from '../../project/entities/project.entity';

/**
 * IP账号实体
 * 
 * 该实体用于管理和存储IP（网络影响力人物）的相关信息，包括基本信息、数据统计以及外部链接等。
 * 主要用途是跟踪和分析特定IP账号的表现数据，为运营决策提供依据。
 * 
 * 实体与Merchant（商户）存在多对多的关联关系，一个IP可以被多个商户使用，一个商户也可以拥有多个IP。
 * 继承自ZtBaseEntity，包含基础字段如id、createdAt、updatedAt等。
 */
@Entity()
export class IpEntity extends ZtBaseEntity {
  /**
   * 对标账号名称
   * 
   * 用于标识当前IP账号所对标（参考/竞争）的目标账号名称
   * 例如：某抖音账号可能将"李佳琦"作为对标账号
   * 该字段必须提供且不能为空
   */
  @ApiProperty({ example: '百度', description: '对标账号名称' })
  @Column()
  @IsNotEmpty()
  benchmarkingAccount: string;

  /**
   * 对标账号地址
   * 
   * 存储对标账号的URL链接，用于直接访问和分析对标账号
   * 如抖音账号链接：https://www.douyin.com/user/MS4wLjABAAAARz7MJzxuIgUFeEDpbMNJMBL2VimELRQvSVt5QZYg
   * 该字段必须提供且不能为空
   */
  @ApiProperty({ example: 'https://www.baidu.com', description: '对标账号' })
  @Column()
  @IsNotEmpty()
  benchmarkingUrl: string;

  /**
   * 对标账号分析
   * 
   * 对该对标账号进行的详细分析文本，包括其内容策略、受众特点、优势劣势等
   * 类型为text以支持长文本内容，该字段必须提供且不能为空
   */
  @ApiProperty({ example: '百度是一个搜索引擎', description: '对标账号分析' })
  @Column('text')
  @IsNotEmpty()
  benchmarkingAnalysis: string;

  /**
   * 抖音UID
   * 
   * 抖音平台分配的用户唯一标识符，通常为纯数字格式
   * 用于在抖音API和其他场景中唯一标识一个用户
   * 该字段可以为空
   */
  @ApiProperty({ description: '抖音UID', nullable: true })
  @Column({ nullable: true })
  uid: string;

  /**
   * 抖音安全用户ID
   * 
   * 抖音平台分配的安全用户ID，格式如：MS4wLjABAAAAXXXXXX
   * 通常在抖音API请求中作为用户标识，比UID更安全
   * 该字段可以为空
   */
  @ApiProperty({ description: '抖音安全用户ID', nullable: true })
  @Column({ nullable: true })
  sec_uid: string;

  /**
   * 用户昵称
   * 
   * 在抖音平台上显示的用户名称
   * 该字段可以为空，用于显示和搜索
   */
  @ApiProperty({ description: '用户昵称', nullable: true })
  @Column({ nullable: true })
  nickname: string;

  /**
   * 用户签名
   * 
   * 抖音用户个人简介或签名文字
   * 该字段可以为空，类型为text以支持长文本
   */
  @ApiProperty({ description: '用户签名', nullable: true })
  @Column({ nullable: true, type: 'text' })
  signature: string;

  /**
   * 用户头像URL
   * 
   * 存储用户头像图片的URL地址
   * 该字段可以为空，用于在界面上显示用户头像
   */
  @ApiProperty({ description: '用户头像URL', nullable: true })
  @Column({ nullable: true, type: 'text' })
  avatar_url: string;

  /**
   * 关注数量
   * 
   * 当前账号在抖音平台上关注的其他用户数量
   * 默认值为0，反映用户主动社交行为
   */
  @ApiProperty({ description: '关注数量', default: 0 })
  @Column({ default: 0 })
  following_count: number;

  /**
   * 粉丝数量
   * 
   * 当前账号在抖音平台上的粉丝数量
   * 默认值为0，是衡量账号影响力的重要指标
   */
  @ApiProperty({ description: '粉丝数量', default: 0 })
  @Column({ default: 0 })
  follower_count: number;

  /**
   * 获赞数量
   * 
   * 当前账号所有作品累计获得的点赞总数
   * 默认值为0，反映内容质量和受欢迎程度
   */
  @ApiProperty({ description: '获赞数量', default: 0 })
  @Column({ default: 0 })
  total_favorited: number;

  /**
   * 作品数量
   * 
   * 当前账号在抖音平台发布的作品总数
   * 默认值为0，反映账号的内容产出能力
   */
  @ApiProperty({ description: '作品数量', default: 0 })
  @Column({ default: 0 })
  aweme_count: number;

  /**
   * IP所在地
   * 
   * 账号显示的IP位置信息，通常格式为"IP属地：XX省"
   * 该字段可以为空，用于地域分析
   */
  @ApiProperty({ description: 'IP所在地', nullable: true })
  @Column({ nullable: true })
  ip_location: string;

  /**
   * 省份
   * 
   * 账号所在的省级行政区
   * 该字段可以为空，用于地域分析和筛选
   */
  @ApiProperty({ description: '省份', nullable: true })
  @Column({ nullable: true })
  province: string;

  /**
   * 城市
   * 
   * 账号所在的市级行政区
   * 该字段可以为空，用于更精细的地域分析
   */
  @ApiProperty({ description: '城市', nullable: true })
  @Column({ nullable: true })
  city: string;

  /**
   * 短ID
   * 
   * 抖音平台分配的短ID，用户可自定义的简短数字ID
   * 该字段可以为空，用于搜索和展示
   */
  @ApiProperty({ description: '短ID', nullable: true })
  @Column({ nullable: true })
  short_id: string;

  /**
   * 项目关联
   * 
   * 与该IP关联的项目列表，表示多对多的关系
   * 一个IP可以被多个项目使用，一个项目也可以拥有多个IP
   * 使用ManyToMany装饰器定义多对多关系
   */
  @ManyToMany(() => Project, (project) => project.ips)
  @JoinColumn()
  projects: Project[];
}
