import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsBoolean } from 'class-validator';
import { ZtBaseReqDto } from '../../../../../utils/baseReq.dto';
import { Transform, Type } from 'class-transformer';

export class IpFindDto extends ZtBaseReqDto {
  /**
   * 对标账号名称
   */
  @ApiProperty({ example: '百度', description: '对标账号名称' })
  @IsString()
  @IsOptional()
  benchmarkingAccount: string;

  /**
   * 对标账号地址
   */
  @ApiProperty({ example: 'https://www.baidu.com', description: '对标账号' })
  @IsOptional()
  @IsString()
  benchmarkingUrl: string;

  /**
   * 根据对标账号的个人定位
   */
  @ApiProperty({ example: '对标账号分析内容', description: '对标账号分析' })
  @IsOptional()
  @IsString()
  benchmarkingAnalysis: string;

  /**
   * 只看我创建的
   */
  @ApiProperty({ example: true, description: '只看当前用户创建的对标账号', required: false })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === true || value === 'true')
  onlyMyCreations?: boolean;

  /**
   * 关注数量
   */
  @ApiProperty({ description: '最小关注数量', required: false })
  @IsOptional()
  @IsNumber()
  min_following_count?: number;

  @ApiProperty({ description: '最大关注数量', required: false })
  @IsOptional()
  @IsNumber()
  max_following_count?: number;

  /**
   * 粉丝数量
   */
  @ApiProperty({ description: '最小粉丝数量', required: false })
  @IsOptional()
  @IsNumber()
  min_follower_count?: number;

  @ApiProperty({ description: '最大粉丝数量', required: false })
  @IsOptional()
  @IsNumber()
  max_follower_count?: number;

  /**
   * 获赞数量
   */
  @ApiProperty({ description: '最小获赞数量', required: false })
  @IsOptional()
  @IsNumber()
  min_total_favorited?: number;

  @ApiProperty({ description: '最大获赞数量', required: false })
  @IsOptional()
  @IsNumber()
  max_total_favorited?: number;

  /**
   * 作品数量
   */
  @ApiProperty({ description: '最小作品数量', required: false })
  @IsOptional()
  @IsNumber()
  min_aweme_count?: number;

  @ApiProperty({ description: '最大作品数量', required: false })
  @IsOptional()
  @IsNumber()
  max_aweme_count?: number;

  /**
   * IP所在地
   */
  @ApiProperty({ description: 'IP所在地', required: false })
  @IsOptional()
  @IsString()
  ip_location?: string;

  /**
   * 省份
   */
  @ApiProperty({ description: '省份', required: false })
  @IsOptional()
  @IsString()
  province?: string;

  /**
   * 城市
   */
  @ApiProperty({ description: '城市', required: false })
  @IsOptional()
  @IsString()
  city?: string;

  /**
   * 短ID
   */
  @ApiProperty({ description: '短ID', required: false })
  @IsOptional()
  @IsString()
  short_id?: string;

  /**
   * 商户ID
   */
  @ApiProperty({ example: 'MERCHANT001', description: '商户id' })
  @IsOptional()
  merchantId: Array<string>;

  /**
   * 对标账号ID
   */
  @ApiProperty({ example: 'BENCHMARKING001', description: '对标账号id' })
  @IsOptional()
  id?: string;
}
