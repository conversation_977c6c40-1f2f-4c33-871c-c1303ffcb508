import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUrl, <PERSON>, Min, Matches } from 'class-validator';
import { Type } from 'class-transformer';

export class SimpleCreateIpDto {
  /**
   * 对标账号地址（抖音链接）
   * @example https://www.douyin.com/user/MS4wLjABAAAAxCIM...
   */
  @ApiProperty({ 
    example: 'https://www.douyin.com/user/MS4wLjABAAAAxCIM...', 
    description: '抖音账号链接，必须是有效的抖音用户主页URL',
    required: true
  })
  @IsNotEmpty({ message: '抖音账号链接不能为空' })
  @IsString({ message: '抖音账号链接必须是字符串' })
  @Matches(/douyin\.com/, { message: '必须是有效的抖音链接' })
  benchmarkingUrl: string;

  /**
   * 需要获取的视频数量
   * @example 20
   */
  @ApiProperty({ 
    example: 20, 
    description: '需要获取的视频数量，范围1-100，默认值为10', 
    default: 10,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({}, { message: '视频数量必须是数字' })
  @Min(1, { message: '视频数量最小为1' })
  @Max(100, { message: '视频数量最大为100' })
  videoLimit?: number;
} 