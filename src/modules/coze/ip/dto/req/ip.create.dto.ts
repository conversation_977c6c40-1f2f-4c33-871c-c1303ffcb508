import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber, ValidateIf } from 'class-validator';

export class CreateIpDto {
  /**
   * 对标账号名称
   */
  @ApiProperty({ example: '百度', description: '对标账号名称' })
  @IsOptional()
  @IsString()
  benchmarkingAccount?: string;

  /**
   * 对标账号地址
   */
  @ApiProperty({ example: 'https://www.douyin.com/user/MS4wLjABAAAAxCIM...', description: '抖音账号链接' })
  @IsNotEmpty({ message: '抖音账号链接不能为空' })
  @IsString()
  benchmarkingUrl: string;

  /**
   * 根据对标账号的个人定位
   */
  @ApiProperty({ example: '对标账号分析内容', description: '对标账号分析' })
  @IsOptional()
  @IsString()
  benchmarkingAnalysis?: string;

  /**
   * 项目ID
   */
  @ApiProperty({ example: 'PROJECT001', description: '项目id' })
  @IsOptional()
  projects?: Array<string>;

  /**
   * 抖音UID
   */
  @ApiProperty({ description: '抖音UID', required: false })
  @IsOptional()
  @IsString()
  uid?: string;

  /**
   * 抖音安全用户ID
   */
  @ApiProperty({ description: '抖音安全用户ID', required: false })
  @IsOptional()
  @IsString()
  sec_uid?: string;

  /**
   * 用户昵称
   */
  @ApiProperty({ description: '用户昵称', required: false })
  @IsOptional()
  @IsString()
  nickname?: string;

  /**
   * 用户签名
   */
  @ApiProperty({ description: '用户签名', required: false })
  @IsOptional()
  @IsString()
  signature?: string;

  /**
   * 用户头像URL
   */
  @ApiProperty({ description: '用户头像URL', required: false })
  @IsOptional()
  @IsString()
  avatar_url?: string;

  /**
   * 关注数量
   */
  @ApiProperty({ description: '关注数量', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  following_count?: number;

  /**
   * 粉丝数量
   */
  @ApiProperty({ description: '粉丝数量', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  follower_count?: number;

  /**
   * 获赞数量
   */
  @ApiProperty({ description: '获赞数量', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  total_favorited?: number;

  /**
   * 作品数量
   */
  @ApiProperty({ description: '作品数量', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  aweme_count?: number;

  /**
   * IP所在地
   */
  @ApiProperty({ description: 'IP所在地', required: false })
  @IsOptional()
  @IsString()
  ip_location?: string;

  /**
   * 省份
   */
  @ApiProperty({ description: '省份', required: false })
  @IsOptional()
  @IsString()
  province?: string;

  /**
   * 城市
   */
  @ApiProperty({ description: '城市', required: false })
  @IsOptional()
  @IsString()
  city?: string;

  /**
   * 短ID
   */
  @ApiProperty({ description: '短ID', required: false })
  @IsOptional()
  @IsString()
  short_id?: string;

  /**
   * 对标账号ID
   */
  @ApiProperty({ example: 'BENCHMARKING001', description: '对标账号id' })
  @IsOptional()
  id?: string;
}
