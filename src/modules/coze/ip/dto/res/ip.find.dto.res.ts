import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

/**
 * IP信息响应DTO
 */
export class IpFindDtoRes {
  @ApiProperty({ description: 'IP ID' })
  id: string;

  @ApiProperty({ description: '对标账号名称' })
  benchmarkingAccount: string;

  @ApiProperty({ description: '对标账号地址' })
  benchmarkingUrl: string;

  @ApiProperty({ description: '对标账号分析' })
  benchmarkingAnalysis: string;

  @ApiProperty({ description: '抖音UID', required: false })
  uid?: string;

  @ApiProperty({ description: '抖音安全用户ID', required: false })
  sec_uid?: string;

  @ApiProperty({ description: '用户昵称', required: false })
  nickname?: string;

  @ApiProperty({ description: '用户签名', required: false })
  signature?: string;

  @ApiProperty({ description: '用户头像URL', required: false })
  avatar_url?: string;

  @ApiProperty({
    description: '根据ipId数组查询所有数据',
    type: String,
    example: 'success',
  })
  @IsOptional()
  ipIds: string[];

  @ApiProperty({ description: '关注数量', required: false })
  following_count: number;

  @ApiProperty({ description: '粉丝数量', required: false })
  follower_count: number;

  @ApiProperty({ description: '获赞数量', required: false })
  total_favorited: number;

  @ApiProperty({ description: '作品数量', required: false })
  aweme_count: number;

  @ApiProperty({ description: 'IP所在地', required: false })
  ip_location?: string;

  @ApiProperty({ description: '省份', required: false })
  province?: string;

  @ApiProperty({ description: '城市', required: false })
  city?: string;

  @ApiProperty({ description: '短ID', required: false })
  short_id?: string;

  @ApiProperty({ description: '状态码', example: 0 })
  status_code: number;

  @ApiProperty({ description: '状态消息', example: '' })
  status_msg: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  /**
   * 构造函数
   * @param data - 源数据对象
   */
  constructor(data: any) {
    if (data) {
      Object.assign(this, data);
    }
  }
}
