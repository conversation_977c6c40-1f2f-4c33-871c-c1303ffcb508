import { ApiProperty } from '@nestjs/swagger';
import { IpEntity } from '../../entities/ip.entity';

/**
 * 视频数据统计结果DTO
 */
export class VideoStatsDto {
  @ApiProperty({
    description: '总视频数',
    example: 10,
  })
  total: number;

  @ApiProperty({
    description: '新增视频数',
    example: 5,
    required: false,
  })
  new?: number;

  @ApiProperty({
    description: '更新视频数',
    example: 3,
    required: false,
  })
  updated?: number;

  @ApiProperty({
    description: '转录视频数',
    example: 5,
    required: false,
  })
  transcribed?: number;
}

/**
 * 视频获取结果DTO
 */
export class VideoResultDto {
  @ApiProperty({
    description: '操作是否成功',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: '结果消息',
    example: '成功更新数据：更新3条现有记录，添加5条新记录',
  })
  message: string;

  @ApiProperty({
    description: '视频数据统计',
    type: VideoStatsDto,
  })
  data: VideoStatsDto;
}

/**
 * 定位分析结果DTO
 */
export class PositioningResultDto {
  @ApiProperty({
    description: '操作是否成功',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: '结果消息',
    example: '账号定位更新成功',
  })
  message: string;

  @ApiProperty({
    description: '定位分析数据',
    type: Object,
  })
  data: {
    total: number;
    updated?: number;
    positioning?: any;
  };
}

/**
 * 简化创建IP响应数据DTO
 */
export class SimpleCreateIpDataDto {
  @ApiProperty({
    description: 'IP账号信息',
    type: IpEntity,
  })
  ip: IpEntity;

  @ApiProperty({
    description: '视频获取结果',
    type: VideoResultDto,
    required: false,
  })
  videos: VideoResultDto;

  @ApiProperty({
    description: '账号定位分析结果',
    type: PositioningResultDto,
    required: false,
  })
  positioning?: PositioningResultDto;
}

/**
 * 简化创建IP响应DTO
 */
export class SimpleCreateIpResDto {
  @ApiProperty({
    description: '状态码',
    example: 200,
  })
  code: number;

  @ApiProperty({
    description: '响应数据',
    type: SimpleCreateIpDataDto,
  })
  data: SimpleCreateIpDataDto;

  @ApiProperty({
    description: '响应消息',
    example: '成功创建账号"抖音账号"并获取10个视频，已自动更新账号定位',
  })
  msg: string;
} 