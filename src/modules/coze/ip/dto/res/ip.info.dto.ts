import { ApiProperty } from '@nestjs/swagger';

/**
 * IP信息DTO
 */
export class IpInfoDto {
  @ApiProperty({ description: 'IP ID' })
  id: string;

  @ApiProperty({ description: '对标账号名称' })
  benchmarkingAccount: string;

  @ApiProperty({ description: '对标账号地址' })
  benchmarkingUrl: string;

  @ApiProperty({ description: '对标账号分析' })
  benchmarkingAnalysis: string;

  @ApiProperty({ description: '关注数量', required: false })
  following_count?: number;

  @ApiProperty({ description: '粉丝数量', required: false })
  follower_count?: number;

  @ApiProperty({ description: '获赞数量', required: false })
  total_favorited?: number;

  @ApiProperty({ description: '作品数量', required: false })
  aweme_count?: number;

  @ApiProperty({ description: 'IP所在地', required: false })
  ip_location?: string;

  @ApiProperty({ description: '省份', required: false })
  province?: string;

  @ApiProperty({ description: '城市', required: false })
  city?: string;

  @ApiProperty({ description: '短ID', required: false })
  short_id?: string;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;
}

/**
 * IP统计信息DTO
 */
export class IpStatisticsDto {
  @ApiProperty({ description: '点赞数量' })
  digg_count: number;

  @ApiProperty({ description: '评论数量' })
  comment_count: number;

  @ApiProperty({ description: '收藏数量' })
  collect_count: number;

  @ApiProperty({ description: '分享数量' })
  share_count: number;
}

/**
 * IP内容项DTO
 */
export class IpItemDto {
  @ApiProperty({ description: '内容ID' })
  id: string;

  @ApiProperty({ description: '内容描述' })
  desc: string;

  @ApiProperty({ description: '封面URL', required: false })
  cover_url?: string;

  @ApiProperty({ description: '创建时间戳' })
  create_time: number;

  @ApiProperty({ description: '内容URL' })
  content_url: string;

  @ApiProperty({ description: 'IP信息' })
  ip: IpInfoDto;

  @ApiProperty({ description: '统计信息' })
  statistics: IpStatisticsDto;
}

/**
 * IP响应DTO
 */
export class IpResponseDto {
  @ApiProperty({
    description: 'IP信息',
    type: IpInfoDto,
    required: false,
    nullable: true,
  })
  ip_info: IpInfoDto | null;

  @ApiProperty({ description: '内容列表', type: [IpItemDto], required: false })
  items?: IpItemDto[];

  @ApiProperty({ description: '分页游标', required: false })
  cursor?: string;

  @ApiProperty({ description: '是否有更多数据', required: false })
  has_more?: boolean;

  @ApiProperty({ description: '状态码', example: 0 })
  status_code: number;

  @ApiProperty({ description: '状态消息', example: '' })
  status_msg: string;
}
