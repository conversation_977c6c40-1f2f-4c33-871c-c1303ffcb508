import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { CommonService } from '../../../../utils/common.service';
import { IpEntity } from '../entities/ip.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, QueryRunner, Repository } from 'typeorm';
import { CreateIpDto } from '../dto/req/ip.create.dto';
import { RemoveReqDto } from '../../../../utils/remove.req.dto';
import { ZtBaseResDto } from '../../../../utils/baseRes.dto';
import { IpFindDtoRes } from '../dto';
import { IpFindDto } from '../dto/req/ip.find.dto';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { DyService } from '../../../dy/services/dy.service';
import { getUserSecId } from '../../../dy/utils';
import { UserInfoDto } from '../../../dy/types';
import { SimpleCreateIpDto } from '../dto/req/ip.simple.create.dto';
import { BenchmarkingService } from '../../benchmarking/services/benchmarking.service';
import { Benchmarking } from '../../benchmarking/entities/benchmarking.entity';
import { CozeWorkflowManager } from '../../coze-workflow.manager';
import { reqUser } from '../../../../utils/nameSpace';

/**
 * IP服务类
 * 提供IP相关的CRUD操作以及与抖音数据集成的功能
 */
@Injectable()
export class IpService extends CommonService {
  private readonly logger = new Logger(IpService.name);

  /**
   * 构造函数
   * @param ipRepository IP仓库
   * @param request 请求对象
   * @param dyService 抖音服务
   * @param benchmarkingService BenchmarkingService
   */
  constructor(
    @InjectRepository(IpEntity)
    private readonly ipRepository: Repository<IpEntity>,
    @Inject(REQUEST) request: Request,
    private readonly dyService: DyService, // 注入抖音服务
    @Inject(forwardRef(() => BenchmarkingService))
    private readonly benchmarkingService: BenchmarkingService, // 使用forwardRef防止循环依赖
    private readonly cozeWorkflowManager: CozeWorkflowManager,
  ) {
    super(request);
  }

  /**
   * 从抖音URL获取用户信息
   * @param url 抖音账号URL
   * @returns 抖音用户信息
   * @throws HttpException 当获取失败时抛出异常
   */
  async fetchDyUserInfo(url: string): Promise<UserInfoDto> {
    try {
      this.logger.log(`正在从URL获取抖音用户信息: ${url}`);

      // 从URL获取sec_user_id
      const secUserId = await getUserSecId(url);
      if (!secUserId) {
        throw new Error('无法从URL中获取抖音用户ID');
      }

      this.logger.log(`成功提取secUserId: ${secUserId}`);

      // 获取用户信息
      const userInfo = await this.dyService.getUserInfo(secUserId);

      this.logger.log(`成功获取抖音用户信息: ${userInfo.nickname}`);
      return userInfo;
    } catch (error) {
      this.logger.error(`获取抖音用户信息失败: ${error.message}`, error.stack);
      throw new HttpException(
        `获取抖音用户信息失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * 根据抖音URL填充IP数据
   * @param ip IP数据对象
   * @returns 填充后的IP数据
   */
  async fillIpDataFromDouyin(
    ip: Partial<CreateIpDto>,
  ): Promise<Partial<CreateIpDto>> {
    // 如果提供了抖音URL，则获取抖音数据
    if (ip.benchmarkingUrl && ip.benchmarkingUrl.includes('douyin.com')) {
      try {
        const dyUserInfo = await this.fetchDyUserInfo(ip.benchmarkingUrl);

        // 填充抖音基础数据
        ip.uid = dyUserInfo.uid;
        ip.sec_uid = dyUserInfo.sec_uid;
        ip.nickname = dyUserInfo.nickname;
        ip.signature = dyUserInfo.signature;
        ip.avatar_url = dyUserInfo.avatar_url;
        
        // 填充统计数据
        ip.following_count = dyUserInfo.following_count;
        ip.follower_count = dyUserInfo.follower_count;
        ip.total_favorited = dyUserInfo.total_favorited;
        ip.aweme_count = dyUserInfo.aweme_count;
        
        // 填充位置相关数据
        ip.ip_location = dyUserInfo.ip_location;
        ip.province = dyUserInfo.province;
        ip.city = dyUserInfo.city;
        ip.short_id = dyUserInfo.short_id;

        // 如果账号名称为空，使用抖音昵称
        if (!ip.benchmarkingAccount) {
          ip.benchmarkingAccount = dyUserInfo.nickname;
        }

        this.logger.log(
          `抖音数据填充成功, 昵称: ${ip.nickname}, 粉丝数: ${ip.follower_count}, 作品数: ${ip.aweme_count}`,
        );
      } catch (error) {
        this.logger.warn(
          `获取抖音数据失败，将使用提供的数据: ${error.message}`,
        );
      }
    }
    return ip;
  }

  /**
   * 创建IP数据
   * @param ipData IP创建DTO
   * @returns 创建结果
   * @throws HttpException 当创建失败时抛出异常
   */
  async createIp(ipData: CreateIpDto): Promise<IpEntity> {
    const queryRunner = this.getQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 尝试从抖音填充数据
      const enrichedIp = await this.fillIpDataFromDouyin(ipData);

      // 创建IP实体
      const entity = this.createIpEntity(enrichedIp);

      // 保存IP实体
      const savedIp = await queryRunner.manager.save(entity);
      this.logger.log(`IP创建成功: ${savedIp.id}`);

      // 添加项目关联
      if (enrichedIp.projects?.length) {
        await this.associateProjects(
          queryRunner,
          savedIp.id,
          enrichedIp.projects,
        );
        this.logger.log(
          `已为IP ${savedIp.id} 关联 ${enrichedIp.projects.length} 个项目`,
        );
      }

      await queryRunner.commitTransaction();
      this.logger.log(`IP创建事务提交成功`);

      return savedIp;
    } catch (error) {
      this.logger.error(`创建IP失败: ${error.message}`, error.stack);
      await queryRunner.rollbackTransaction();
      throw new HttpException(
        `创建IP失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 更新IP数据
   * @param id IP ID
   * @param ip IP更新数据
   * @returns 无返回值
   */
  async update(id: string, ip: Partial<IpEntity>): Promise<void> {
    return this.updateEntity(this.ipRepository, id, ip, 'IP');
  }

  /**
   * 获取数据库查询运行器
   * @returns TypeORM查询运行器
   */
  getQueryRunner(): QueryRunner {
    return this.ipRepository.manager.connection.createQueryRunner();
  }

  /**
   * 刷新IP数据，从抖音获取最新数据
   * @param id IP ID
   * @returns 更新后的IP实体
   * @throws HttpException 当刷新失败时抛出异常
   */
  async refreshIpData(id: string): Promise<IpEntity> {
    this.logger.log(`开始刷新IP数据, ID: ${id}`);
    try {
      // 获取当前IP信息
      const ip = await this.findOne(id);
      if (!ip) {
        throw new Error('IP不存在');
      }

      // 检查是否是抖音URL
      if (!ip.benchmarkingUrl || !ip.benchmarkingUrl.includes('douyin.com')) {
        throw new Error('不是有效的抖音URL，无法刷新数据');
      }

      this.logger.log(`开始获取抖音数据, URL: ${ip.benchmarkingUrl}`);

      // 获取抖音数据
      const dyUserInfo = await this.fetchDyUserInfo(ip.benchmarkingUrl);

      // 更新字段
      const updateData: Partial<IpEntity> = {
        // 更新抖音基础信息
        uid: dyUserInfo.uid,
        sec_uid: dyUserInfo.sec_uid,
        nickname: dyUserInfo.nickname,
        signature: dyUserInfo.signature,
        avatar_url: dyUserInfo.avatar_url,
        
        // 更新统计数据
        following_count: dyUserInfo.following_count,
        follower_count: dyUserInfo.follower_count,
        total_favorited: dyUserInfo.total_favorited,
        aweme_count: dyUserInfo.aweme_count,
        
        // 更新位置相关信息
        ip_location: dyUserInfo.ip_location,
        province: dyUserInfo.province,
        city: dyUserInfo.city,
        short_id: dyUserInfo.short_id,
      };

      this.logger.log(
        `更新IP数据: 昵称: ${dyUserInfo.nickname}, 粉丝数从 ${ip.follower_count} 更新为 ${updateData.follower_count}, 头像: ${updateData.avatar_url?.substring(0, 30)}...`,
      );

      // 更新数据库
      await this.update(id, updateData);

      // 返回更新后的数据
      return await this.findOne(id);
    } catch (error) {
      this.logger.error(`刷新IP数据失败: ${error.message}`, error.stack);
      throw new HttpException(
        `刷新IP数据失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * 更新IP及其关联的商户
   * @param ipData IP更新DTO
   * @returns 无返回值
   * @throws HttpException 当更新失败时抛出异常
   */
  async updateIpWithProjects(ipData: Partial<CreateIpDto>): Promise<void> {
    if (!ipData.id) {
      throw new HttpException('IP ID不能为空', HttpStatus.BAD_REQUEST);
    }

    this.logger.log(`开始更新IP, ID: ${ipData.id}`);

    // 尝试从抖音填充数据
    const enrichedIp = await this.fillIpDataFromDouyin(ipData);

    const queryRunner = this.getQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 准备更新数据
      const updateData = this.prepareUpdateData(enrichedIp);

      // 执行更新操作
      await queryRunner.manager.update(IpEntity, enrichedIp.id, updateData);
      this.logger.log(`IP基本信息更新成功`);

      // 如果有项目ID，处理关联关系
      if (enrichedIp.projects?.length) {
        await this.updateProjectAssociations(
          queryRunner,
          enrichedIp.id,
          enrichedIp.projects,
        );
        this.logger.log(`IP项目关联更新成功`);
      }

      await queryRunner.commitTransaction();
      this.logger.log(`IP更新事务提交成功`);
    } catch (err) {
      this.logger.error(`更新IP失败: ${err.message}`, err.stack);
      await queryRunner.rollbackTransaction();
      throw new HttpException(
        `更新IP失败: ${err.message}`,
        HttpStatus.BAD_REQUEST,
      );
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * 删除IP
   * @param params 删除请求DTO
   * @returns 无返回值
   */
  async delete(params: RemoveReqDto): Promise<void> {
    this.logger.log(`删除IP, ID: ${params.id}`);
    
    try {
      // 如果提供了ID
      if (params.id) {
        // 1. 查找相关的视频数据
        const videos = await this.benchmarkingService.findAll({ ipId: params.id });
        
        if (videos && videos.length > 0) {
          this.logger.log(`找到 ${videos.length} 条相关视频数据，准备删除`);
          
          // 2. 删除相关的视频数据
          // 创建一个包含所有视频ID的删除请求
          const videoDeleteParams: RemoveReqDto = {
            idList: videos.map(video => video.id)
          };
          
          // 使用benchmarkingService的删除方法删除视频
          await this.benchmarkingService.deleteBenchmarking(videoDeleteParams);
          
          this.logger.log(`已删除IP ${params.id} 相关的所有视频数据`);
        }
      }
      
      // 3. 删除IP账号
      await this.deleteEntity(this.ipRepository, params, 'IP');
      
      this.logger.log(`IP账号删除成功`);
    } catch (error) {
      this.logger.error(`删除IP账号失败: ${error.message}`, error.stack);
      throw new HttpException(
        `删除IP账号失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 查询IP列表
   * @param filter 查询条件
   * @returns 分页结果
   */
  async find(filter: Partial<IpFindDto>): Promise<ZtBaseResDto> {
    this.logger.log(`查询IP列表，页码: ${filter.pageIndex || 1}`);
    return this.findWithPagination(
      this.ipRepository,
      filter,
      IpFindDtoRes,
      { 
        authMode: 'createdBy',
        relations: ['projects']
      }
    );
  }

  /**
   * 查找所有IP
   * @param filter 过滤条件
   * @returns IP实体数组
   */
  async findAll(filter: Partial<IpFindDtoRes> = {}): Promise<IpEntity[]> {
    this.logger.log('查询所有IP');
    return this.findAllEntities(
      this.ipRepository,
      filter,
      { sortBy: 'createdAt', sortOrder: 'descend' },
      { 
        authMode: 'createdBy',
        relations: ['projects']
      }
    );
  }

  /**
   * 根据ID查找单个IP
   * @param id IP ID
   * @returns IP实体
   * @throws HttpException 当查询失败或IP不存在时抛出异常
   */
  async findOne(id: string): Promise<IpEntity> {
    this.logger.log(`根据ID查询IP: ${id}`);
    try {
      const ip = await this.ipRepository.findOne({
        where: { id },
        relations: ['projects'],
      });

      if (!ip) {
        this.logger.warn(`IP不存在, ID: ${id}`);
        throw new HttpException('IP不存在', HttpStatus.NOT_FOUND);
      }

      return ip;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      this.logger.error(`查询IP失败: ${error.message}`, error.stack);
      throw new HttpException(
        `查询IP失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * 创建IP实体对象
   * @param ipDto IP创建DTO
   * @returns IP实体
   */
  private createIpEntity(ipDto: Partial<CreateIpDto>): IpEntity {
    // 获取当前用户信息
    const currentUser = this.request[reqUser];
    const userId = currentUser?.id || 'system';
    
    const entity = new IpEntity();
    entity.benchmarkingAccount = ipDto.benchmarkingAccount;
    entity.benchmarkingUrl = ipDto.benchmarkingUrl;
    entity.benchmarkingAnalysis = ipDto.benchmarkingAnalysis;

    // 添加抖音基础信息字段
    entity.uid = ipDto.uid;
    entity.sec_uid = ipDto.sec_uid;
    entity.nickname = ipDto.nickname;
    entity.signature = ipDto.signature;
    entity.avatar_url = ipDto.avatar_url;

    // 添加统计数据字段，使用默认值确保非空
    entity.following_count = ipDto.following_count || 0;
    entity.follower_count = ipDto.follower_count || 0;
    entity.total_favorited = ipDto.total_favorited || 0;
    entity.aweme_count = ipDto.aweme_count || 0;

    // 添加位置相关字段
    entity.ip_location = ipDto.ip_location;
    entity.province = ipDto.province;
    entity.city = ipDto.city;
    entity.short_id = ipDto.short_id;
    
    // 设置创建者和更新者字段
    entity.createdBy = userId;
    entity.updatedBy = userId;

    return entity;
  }

  /**
   * 关联项目和IP
   * @param queryRunner 查询运行器
   * @param ipId IP ID
   * @param projectIds 项目ID数组
   */
  private async associateProjects(
    queryRunner: QueryRunner,
    ipId: string,
    projectIds: string[],
  ): Promise<void> {
    await queryRunner.manager
      .createQueryBuilder()
      .insert()
      .into('project_ips_ip_entity')
      .values(
        projectIds.map((projectId) => ({
          projectId: projectId,
          ipEntityId: ipId,
        })),
      )
      .execute();
  }

  /**
   * 准备IP更新数据
   * @param ip IP更新DTO
   * @returns 过滤后的更新数据对象
   */
  private prepareUpdateData(ip: Partial<CreateIpDto>): Partial<IpEntity> {
    const updateData: Partial<IpEntity> = {
      // 基础字段
      benchmarkingAccount: ip.benchmarkingAccount,
      benchmarkingUrl: ip.benchmarkingUrl,
      benchmarkingAnalysis: ip.benchmarkingAnalysis,
      
      // 抖音用户基础信息
      uid: ip.uid,
      sec_uid: ip.sec_uid,
      nickname: ip.nickname,
      signature: ip.signature,
      avatar_url: ip.avatar_url,
      
      // 统计数据
      following_count: ip.following_count,
      follower_count: ip.follower_count,
      total_favorited: ip.total_favorited,
      aweme_count: ip.aweme_count,
      
      // 位置相关信息
      ip_location: ip.ip_location,
      province: ip.province,
      city: ip.city,
      short_id: ip.short_id,
    };

    // 过滤掉undefined的值
    Object.keys(updateData).forEach(
      (key) => updateData[key] === undefined && delete updateData[key],
    );

    return updateData;
  }

  /**
   * 更新IP和项目的关联关系
   * @param queryRunner 查询运行器
   * @param ipId IP ID
   * @param projectIds 项目ID数组
   */
  private async updateProjectAssociations(
    queryRunner: QueryRunner,
    ipId: string,
    projectIds: string[],
  ): Promise<void> {
    // 删除旧的关联关系
    await queryRunner.manager
      .createQueryBuilder()
      .delete()
      .from('project_ips_ip_entity')
      .where('ipEntityId = :ipId', { ipId })
      .execute();

    // 添加新的关联关系
    await this.associateProjects(queryRunner, ipId, projectIds);
  }

  /**
   * 构建IP查询
   * @param filter 查询条件
   * @returns TypeORM查询构建器
   */
  private buildQuery(filter: Partial<IpFindDto>) {
    // 清理过滤对象
    const cleanFilter = { ...filter };
    delete cleanFilter.pageIndex;
    delete cleanFilter.pageSize;
    delete cleanFilter.sort;
    delete cleanFilter.merchantId;
    delete cleanFilter.onlyMyCreations;

    // 删除范围查询的字段
    delete cleanFilter.min_following_count;
    delete cleanFilter.max_following_count;
    delete cleanFilter.min_follower_count;
    delete cleanFilter.max_follower_count;
    delete cleanFilter.min_total_favorited;
    delete cleanFilter.max_total_favorited;
    delete cleanFilter.min_aweme_count;
    delete cleanFilter.max_aweme_count;

    const queryBuilder = this.ipRepository
      .createQueryBuilder('ip')
      .leftJoinAndSelect('ip.projects', 'projects');

    // 始终添加当前用户的过滤条件
    const currentUser = this.request[reqUser];
    if (currentUser?.id) {
      queryBuilder.andWhere('ip.createdBy = :currentUserId', {
        currentUserId: currentUser.id,
      });
      this.logger.log(`应用固定筛选条件：只看当前用户(${currentUser.id})创建的对标账号`);
    } else {
      this.logger.warn('无法获取当前用户ID，将返回空结果');
      // 如果获取不到当前用户ID，设置一个不可能匹配的条件，确保返回空结果而不是全部结果
      queryBuilder.andWhere('ip.createdBy = :invalidId', { invalidId: 'current-user-not-found' });
    }

    // 基本字段过滤
    Object.keys(cleanFilter).forEach((key) => {
      if (cleanFilter[key] !== undefined && cleanFilter[key] !== null) {
        queryBuilder.andWhere(`ip.${key} = :${key}`, {
          [key]: cleanFilter[key],
        });
      }
    });

    // 添加范围查询条件
    this.applyRangeFilters(queryBuilder, filter);

    return queryBuilder;
  }

  /**
   * 应用范围查询条件
   * @param queryBuilder 查询构建器
   * @param filter 过滤条件
   */
  private applyRangeFilters(queryBuilder, filter: Partial<IpFindDto>) {
    // 关注数范围
    if (filter.min_following_count !== undefined) {
      queryBuilder.andWhere('ip.following_count >= :minFollowing', {
        minFollowing: filter.min_following_count,
      });
    }

    if (filter.max_following_count !== undefined) {
      queryBuilder.andWhere('ip.following_count <= :maxFollowing', {
        maxFollowing: filter.max_following_count,
      });
    }

    // 粉丝数范围
    if (filter.min_follower_count !== undefined) {
      queryBuilder.andWhere('ip.follower_count >= :minFollower', {
        minFollower: filter.min_follower_count,
      });
    }

    if (filter.max_follower_count !== undefined) {
      queryBuilder.andWhere('ip.follower_count <= :maxFollower', {
        maxFollower: filter.max_follower_count,
      });
    }

    // 获赞数范围
    if (filter.min_total_favorited !== undefined) {
      queryBuilder.andWhere('ip.total_favorited >= :minFavorited', {
        minFavorited: filter.min_total_favorited,
      });
    }

    if (filter.max_total_favorited !== undefined) {
      queryBuilder.andWhere('ip.total_favorited <= :maxFavorited', {
        maxFavorited: filter.max_total_favorited,
      });
    }

    // 作品数范围
    if (filter.min_aweme_count !== undefined) {
      queryBuilder.andWhere('ip.aweme_count >= :minAweme', {
        minAweme: filter.min_aweme_count,
      });
    }

    if (filter.max_aweme_count !== undefined) {
      queryBuilder.andWhere('ip.aweme_count <= :maxAweme', {
        maxAweme: filter.max_aweme_count,
      });
    }
  }

  /**
   * 简化创建IP并获取视频
   * 只需要提供抖音URL，即可创建IP并自动获取视频
   * @param data 包含抖音URL和视频数量限制的DTO
   * @returns 创建的IP和视频获取结果
   */
  async simpleCreateIpWithVideos(data: SimpleCreateIpDto): Promise<{
    success: boolean;
    message: string;
    data: {
      ip: IpEntity;
      videos: any;
      positioning?: any;
    };
  }> {
    const { benchmarkingUrl, videoLimit = 10 } = data;
    
    this.logger.log(`开始简化创建IP并获取视频, URL: ${benchmarkingUrl}, 视频限制: ${videoLimit}`);
    
    try {
      // 获取当前用户信息
      const currentUser = this.request[reqUser];
      const userId = currentUser?.id;
      
      if (!userId) {
        throw new HttpException(
          '无法获取当前用户信息',
          HttpStatus.UNAUTHORIZED
        );
      }
      
      // 1. 检查URL是否已存在于当前用户创建的账号中
      const existingIp = await this.ipRepository.findOne({
        where: { 
          benchmarkingUrl,
          createdBy: userId // 添加创建者过滤，实现数据隔离
        }
      });
      
      if (existingIp) {
        throw new HttpException(
          '您已创建过该抖音账号',
          HttpStatus.BAD_REQUEST
        );
      }
      
      // 2. 从抖音获取账号信息
      const dyUserInfo = await this.fetchDyUserInfo(benchmarkingUrl);
      
      if (!dyUserInfo) {
        throw new HttpException(
          '无法从抖音URL获取账号信息',
          HttpStatus.BAD_REQUEST
        );
      }
      
      // 3. 创建完整的IP对象
      const ipData: CreateIpDto = {
        benchmarkingUrl,
        benchmarkingAccount: dyUserInfo.nickname || '抖音账号',
        benchmarkingAnalysis: '自动创建的账号，尚未进行定位分析', // 使用简单字符串
        projects: [], // 提供空数组避免null        
        // 填充从抖音获取的数据
        uid: dyUserInfo.uid,
        sec_uid: dyUserInfo.sec_uid,
        nickname: dyUserInfo.nickname,
        signature: dyUserInfo.signature,
        avatar_url: dyUserInfo.avatar_url,
        following_count: dyUserInfo.following_count,
        follower_count: dyUserInfo.follower_count,
        total_favorited: dyUserInfo.total_favorited,
        aweme_count: dyUserInfo.aweme_count,
        ip_location: dyUserInfo.ip_location,
        province: dyUserInfo.province,
        city: dyUserInfo.city,
        short_id: dyUserInfo.short_id,
      };
      
      // 4. 创建IP账号
      this.logger.log(`开始创建IP账号: ${ipData.nickname}`);
      const createdIp = await this.createIp(ipData);
      
      if (!createdIp) {
        throw new HttpException(
          '创建IP账号失败',
          HttpStatus.INTERNAL_SERVER_ERROR
        );
      }
      
      this.logger.log(`IP账号创建成功, ID: ${createdIp.id}, 创建者: ${userId}`);
      
      try {
        // 5. 获取视频 (只获取视频基本信息，不处理文案)
        this.logger.log(`开始获取视频基本信息, 账号ID: ${createdIp.id}, 数量限制: ${videoLimit}`);
        
        // 调用自定义方法获取视频，避免调用文案处理
        const videosResult = await this.getVideosWithoutAudioText(createdIp.id, videoLimit);
        
        // 6. 立即启动异步处理，用后台处理文案和账号定位
        setTimeout(() => {
          this.processFullWorkflow(createdIp.id, videoLimit).catch(error => {
            this.logger.error(`异步处理完整工作流失败: ${error.message}`, error.stack);
          });
        }, 0);
        
        // 7. 立即返回结果，不等待异步处理
        return {
          success: true,
          message: `成功创建账号"${createdIp.benchmarkingAccount}"并获取${videosResult?.total || 0}个视频，文案生成和账号定位将在后台依次处理`,
          data: {
            ip: createdIp,
            videos: {
              success: true,
              message: `成功获取${videosResult?.total || 0}个视频，文案将依次处理后再进行账号定位`,
              data: videosResult
            }
          }
        };
      } catch (videoError) {
        // 如果获取视频失败，仍然返回成功创建的账号
        this.logger.error(`获取视频失败，但账号已成功创建: ${videoError.message}`);
        return {
          success: true,
          message: `账号"${createdIp.benchmarkingAccount}"已创建成功，但获取视频失败: ${videoError.message}`,
          data: {
            ip: createdIp,
            videos: null
          }
        };
      }
    } catch (error) {
      this.logger.error(`简化创建IP并获取视频失败: ${error.message}`, error.stack);
      
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        `简化创建IP并获取视频失败: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * 仅获取视频信息，不处理语音文案
   * 避免调用processAudioTextAsync，实现快速返回
   * @param ipId IP账号ID
   * @param limit 限制数量
   * @private
   */
  private async getVideosWithoutAudioText(ipId: string, limit: number): Promise<any> {
    try {
      // 获取IP信息
      const ip = await this.ipRepository.findOne({
        where: { id: ipId },
        relations: ['projects'],
      });
      if (!ip) {
        throw new Error(`找不到ID为 ${ipId} 的IP账号`);
      }

      // 验证是否有抖音URL
      if (!ip.benchmarkingUrl || !ip.benchmarkingUrl.includes('douyin.com')) {
        throw new Error('无效的抖音URL，无法获取视频数据');
      }

      // 使用DyService获取最新的账号数据
      const accountData = await this.dyService.getAccountData({
        user_url: ip.benchmarkingUrl,
        tab: 'post',
        count: limit || 20,
        pages: 1,
      });

      if (!accountData?.items?.length) {
        return { total: 0, message: '没有找到视频数据' };
      }

      // 过滤有音频地址的数据
      const filteredItems = accountData.items.filter(item => Boolean(item.audioUrl));
      const processItems = limit && filteredItems.length > limit
        ? filteredItems.slice(0, limit)
        : filteredItems;

      // 获取所有视频的awemeId
      const awemeIds = processItems.map(item => item.id);

      // 查询数据库中已经存在的记录
      const existingRecords = await this.benchmarkingService.findAll({ ipId });
      
      // 过滤出匹配的记录
      const filteredExistingRecords = existingRecords.filter(record => 
        awemeIds.includes(record.awemeId)
      );

      // 创建映射
      const existingAwemeIdMap = filteredExistingRecords.reduce<Record<string, Benchmarking>>((acc, record) => {
        acc[record.awemeId] = record;
        return acc;
      }, {});

      // 分类处理项目
      const newItems: Benchmarking[] = [];
      const updateItems: Benchmarking[] = [];

      // 遍历处理每个视频
      for (const item of processItems) {
        const existingRecord = existingAwemeIdMap[item.id];

        // 将时间戳转换为格式化的日期时间字符串
        const formattedCreateTime = this.formatTimestamp(item.create_time);
        this.logger.log(`视频 ${item.id} 的原始时间戳: ${item.create_time}, 格式化后: ${formattedCreateTime}`);

        if (existingRecord) {
          // 更新现有记录的基本字段
          existingRecord.description = item.desc || existingRecord.description;
          existingRecord.videoUrl = item.video_url || existingRecord.videoUrl;
          existingRecord.audioUrl = item.audioUrl || existingRecord.audioUrl;
          existingRecord.upName = item.author?.nickname || existingRecord.upName || '';
          existingRecord.type = item.type || existingRecord.type || 'video';
          existingRecord.createTime = formattedCreateTime; // 更新格式化后的创建时间
          
          // 更新统计数据
          existingRecord.diggCount = item.statistics?.digg_count || existingRecord.diggCount || 0;
          existingRecord.commentCount = item.statistics?.comment_count || existingRecord.commentCount || 0;
          existingRecord.collectCount = item.statistics?.collect_count || existingRecord.collectCount || 0;
          existingRecord.shareCount = item.statistics?.share_count || existingRecord.shareCount || 0;

          // 添加到更新列表
          updateItems.push(existingRecord);
        } else {
          // 创建新记录
          const newRecord = new Benchmarking();
          newRecord.awemeId = item.id;
          newRecord.description = item.desc || '';
          newRecord.videoUrl = item.video_url || '';
          newRecord.audioUrl = item.audioUrl || '';
          newRecord.upName = item.author?.nickname || '';
          newRecord.type = item.type || 'video';
          newRecord.ipId = ipId;
          newRecord.audioText = '待处理'; // 设置初始状态，不使用"文案生成中..."，避免与实际处理状态混淆
          newRecord.createTime = formattedCreateTime; // 设置格式化后的创建时间

          // 添加统计数据
          newRecord.diggCount = item.statistics?.digg_count || 0;
          newRecord.commentCount = item.statistics?.comment_count || 0;
          newRecord.collectCount = item.statistics?.collect_count || 0;
          newRecord.shareCount = item.statistics?.share_count || 0;

          // 添加到新记录列表
          newItems.push(newRecord);
        }
      }

      // 保存实体数据
      const entities: Benchmarking[] = [...newItems, ...updateItems];
      if (entities.length > 0) {
        await this.benchmarkingService.createBenchmarking(entities);
      }

      return {
        total: entities.length,
        new: newItems.length,
        updated: updateItems.length,
        items: processItems.map(item => ({
          id: item.id,
          description: item.desc,
          videoUrl: item.video_url,
          audioUrl: item.audioUrl,
          upName: item.author?.nickname,
          createTime: this.formatTimestamp(item.create_time) // 返回格式化后的创建时间
        }))
      };
    } catch (error) {
      this.logger.error(`获取视频数据失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 将时间戳转换为 YYYY-MM-DD hh:mm:ss 格式的字符串
   * @param timestamp 时间戳（秒）
   * @returns 格式化后的日期时间字符串
   */
  private formatTimestamp(timestamp: number): string {
    if (!timestamp) return '';
    
    try {
      // 创建日期对象（乘以1000转换为毫秒）
      const date = new Date(timestamp * 1000);
      
      // 获取年月日
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      // 获取时分秒
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      // 组合成格式化的字符串
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
      this.logger.error(`时间戳转换失败: ${error.message}`, error.stack);
      return '';
    }
  }

  /**
   * 处理完整的工作流程：刷新视频、提取文案、更新账号定位
   * 该方法会在后台异步运行，不会阻塞接口返回
   * @param ipId IP账号ID
   * @param videoLimit 视频数量限制
   * @private
   */
  private async processFullWorkflow(ipId: string, videoLimit: number): Promise<void> {
    try {
      this.logger.log(`开始处理完整后台工作流，账号ID: ${ipId}`);
      
      // 1. 获取该账号下的所有视频
      const videos = await this.benchmarkingService.findAll({ ipId });
      
      if (!videos || videos.length === 0) {
        this.logger.warn(`账号ID ${ipId} 没有关联的视频，无法处理文案和账号定位`);
        return;
      }
      
      // 2. 过滤出有音频URL的视频
      const videosWithAudio = videos.filter(video => video.audioUrl);
      
      if (videosWithAudio.length === 0) {
        this.logger.warn(`账号ID ${ipId} 没有可处理的音频，无法提取文案`);
        return;
      }
      
      this.logger.log(`找到 ${videosWithAudio.length} 个视频需要处理文案`);
      
      // 3. 逐个处理每个视频的文案提取（同步处理，一个接一个）
      for (const video of videosWithAudio) {
        try {
          // 设置状态为处理中
          await this.benchmarkingService.updateBenchmarking(
            video.id,
            { audioText: '文案生成中...' }
          );
          
          // 调用Coze工作流
          this.logger.log(`开始处理视频 ${video.id} 的文案，音频URL: ${video.audioUrl.substring(0, 50)}...`);
          
          const audioText = await this.cozeWorkflowManager.extractAudioText({
            input: video.audioUrl
          }, true); // 使用备用工作流
          
          // 更新数据库
          await this.benchmarkingService.updateBenchmarking(
            video.id,
            { audioText }
          );
          
          this.logger.log(`视频 ${video.id} 的文案已更新，文案长度: ${audioText.length}`);
          
          // 添加小延迟，避免API请求过于频繁
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (videoError) {
          this.logger.error(`处理视频 ${video.id} 文案失败: ${videoError.message}`);
          
          // 如果处理失败，更新状态为失败
          try {
            await this.benchmarkingService.updateBenchmarking(
              video.id,
              { audioText: '文案生成失败' }
            );
          } catch (updateError) {
            this.logger.error(`更新视频 ${video.id} 状态失败: ${updateError.message}`);
          }
        }
      }
      
      this.logger.log(`所有视频文案处理完成，共处理 ${videosWithAudio.length} 个视频，准备更新账号定位`);
      
      // 4. 所有文案处理完成后，再处理账号定位
      try {
        const positioningResult = await this.benchmarkingService.updatePositioning({ ipId });
        
        if (positioningResult.success) {
          this.logger.log(`账号定位更新成功，账号ID: ${ipId}`);
          
          // 获取最新的IP信息
          const updatedIp = await this.findOne(ipId);
          
          if (updatedIp && updatedIp.benchmarkingAnalysis) {
            this.logger.log(`账号定位分析结果已更新: ${typeof updatedIp.benchmarkingAnalysis === 'object' ? 
              JSON.stringify(updatedIp.benchmarkingAnalysis).substring(0, 100) + '...' : 
              updatedIp.benchmarkingAnalysis.substring(0, 100) + '...'}`);
          }
        } else {
          this.logger.warn(`账号定位更新失败: ${positioningResult.message}`);
        }
      } catch (positioningError) {
        this.logger.error(`更新账号定位失败: ${positioningError.message}`, positioningError.stack);
      }
      
      this.logger.log(`完整后台工作流处理完成，账号ID: ${ipId}`);
    } catch (error) {
      this.logger.error(`处理完整工作流失败: ${error.message}`, error.stack);
    }
  }
}
