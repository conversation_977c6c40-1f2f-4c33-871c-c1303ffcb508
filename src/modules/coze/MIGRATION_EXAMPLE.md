# Coze工作流管理器迁移示例

本文档展示如何从直接调用 `runWorkflow` 迁移到使用统一的 `CozeWorkflowManager`。

## 迁移步骤

### 1. 导入新的工作流管理器

**之前：**
```typescript
import { runWorkflow } from '../../../../utils/common';
```

**现在：**
```typescript
import { CozeWorkflowManager } from '../../coze-workflow.manager';
```

### 2. 在构造函数中注入管理器

**之前：**
```typescript
@Injectable()
export class VideoService extends CommonService {
  constructor(
    @InjectRepository(Video)
    private readonly videoRepository: Repository<Video>,
    // ... 其他注入
  ) {
    super(request);
  }
}
```

**现在：**
```typescript
@Injectable()
export class VideoService extends CommonService {
  constructor(
    @InjectRepository(Video)
    private readonly videoRepository: Repository<Video>,
    // ... 其他注入
    private cozeWorkflowManager: CozeWorkflowManager,
  ) {
    super(request);
  }
}
```

## 具体迁移案例

### 案例1: 视频内容生成

**之前：**
```typescript
const res: any = await runWorkflow('7511339947418746892', {
  account_positioning: merchant.accountPositioning,
  knowledge: merchant.knowledge,
  qa: qa,
  project_name: merchant.projectName,
  human: human,
  benchmarking_list: benchmarking,
  hot: hot,
  limit,
  date,
});
```

**现在：**
```typescript
const res: any = await this.cozeWorkflowManager.generateVideoContent({
  account_positioning: merchant.accountPositioning,
  knowledge: merchant.knowledge,
  qa: qa,
  project_name: merchant.projectName,
  human: human,
  benchmarking_list: benchmarking,
  hot: hot,
  limit,
  date,
});
```

### 案例2: 视频文件生成

**之前：**
```typescript
const videoUrl = await runWorkflow('7512365664541097999', {
  segment: JSON.parse(video.segment),
  merchant_id: video.merchantId,
  human_id: video.humanId,
});
```

**现在：**
```typescript
const videoUrl = await this.cozeWorkflowManager.generateVideoFile({
  segment: JSON.parse(video.segment),
  merchant_id: video.merchantId,
  human_id: video.humanId,
});
```

### 案例3: 热点内容获取

**之前：**
```typescript
const res = await runWorkflow('7509513076439302194', {
  merchant_id: filter.merchantId,
  input: filter.text,
  date: filter.date,
});
```

**现在：**
```typescript
const res = await this.cozeWorkflowManager.fetchHotContent({
  merchant_id: filter.merchantId,
  input: filter.text,
  date: filter.date,
});
```

### 案例4: 商户数据操作

**之前：**
```typescript
if (isEdit) {
  return await runWorkflow('7510206475869093928', obj);
} else {
  return await runWorkflow('7499089012003684393', obj);
}
```

**现在：**
```typescript
if (isEdit) {
  return await this.cozeWorkflowManager.editMerchantData(obj);
} else {
  return await this.cozeWorkflowManager.createMerchantData(obj);
}
```

### 案例5: 商户数据删除

**之前：**
```typescript
async syncDataDelete(id: string) {
  return await runWorkflow('7510975886942879756', {
    merchant_id: id,
  });
}
```

**现在：**
```typescript
async syncDataDelete(id: string) {
  return await this.cozeWorkflowManager.deleteMerchantData(id);
}
```

### 案例6: 音频文案提取

**之前：**
```typescript
// 调用Coze工作流
const workflowId = '7511265128399749131';
const result = await runWorkflow(workflowId, {
  input: video.audioUrl
});

// 处理结果
let audioText = '无法识别';
if (result && typeof result === 'string') {
  audioText = result;
} else if (result && result.text) {
  audioText = result.text;
} else if (result && Array.isArray(result) && result.length > 0) {
  audioText = result[0].text || result[0].result || '无法识别';
}
```

**现在：**
```typescript
// 调用Coze工作流 (结果处理已内置)
const audioText = await this.cozeWorkflowManager.extractAudioText({
  input: video.audioUrl
}, true); // 使用备用工作流
```

### 案例7: 材料处理

**之前：**
```typescript
async syncData() {
  const res = await runWorkflow('7510518588304031756', {});
  // ... 处理逻辑
}
```

**现在：**
```typescript
async syncData() {
  const res = await this.cozeWorkflowManager.processMaterial();
  // ... 处理逻辑
}
```

### 案例8: 人物处理

**之前：**
```typescript
async syncData() {
  const res = await runWorkflow('7510520073611624484', {});
  // ... 处理逻辑
}
```

**现在：**
```typescript
async syncData() {
  const res = await this.cozeWorkflowManager.processHuman();
  // ... 处理逻辑
}
```

### 案例9: 问答处理

**之前：**
```typescript
const res: any = await runWorkflow('7507992502454452224', {
  knowledge: merchant.knowledge,
  projectName: merchant.projectName,
  limit: cozeDto.limit,
  qas,
});
```

**现在：**
```typescript
const res: any = await this.cozeWorkflowManager.processQA({
  knowledge: merchant.knowledge,
  projectName: merchant.projectName,
  limit: cozeDto.limit,
  qas,
});
```

### 案例10: 账号定位分析

**之前：**
```typescript
const result = await runWorkflow('7517285166243512346', {
  ip_account: ipAccount,
  video_list: sampledVideoList,
});
```

**现在：**
```typescript
const result = await this.cozeWorkflowManager.analyzeAccountPositioning({
  ip_id: ipAccount,
  videos: sampledVideoList,
});
```

## 迁移的好处

1. **类型安全**: TypeScript 类型检查防止参数错误
2. **统一管理**: 所有工作流ID集中管理，便于维护
3. **错误处理**: 统一的错误处理和日志记录
4. **代码可读性**: 方法名更具描述性，代码更易理解
5. **参数验证**: 预定义的接口确保参数完整性
6. **文档化**: 每个方法都有详细的注释和参数说明

## 注意事项

1. 确保在模块中正确注册了 `CozeWorkflowManager`
2. 某些复杂的结果处理逻辑可能需要调整
3. 如果有新的工作流，记得在枚举中添加对应的ID
4. 备用工作流的使用需要根据具体业务需求决定