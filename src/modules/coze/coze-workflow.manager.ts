import { Injectable, Logger } from '@nestjs/common';
import { runWorkflow } from '../../utils/common';

/**
 * Coze工作流ID枚举
 * 集中管理所有工作流ID，方便维护和新增
 */
export enum CozeWorkflowId {
  /** 视频内容生成工作流 */
  VIDEO_CONTENT_GENERATION = '7511339947418746892',
  
  /** 视频文件生成工作流 */
  VIDEO_FILE_GENERATION = '7512365664541097999',
  
  /** 热点获取工作流 */
  HOT_CONTENT_FETCH = '7509513076439302194',
  
  /** 项目数据创建工作流 */
  PROJECT_DATA_CREATE = '7499089012003684393',
  
  /** 项目数据编辑工作流 */
  PROJECT_DATA_EDIT = '7510206475869093928',
  
  /** 项目数据删除工作流 */
  PROJECT_DATA_DELETE = '7510975886942879756',
  
  /** 音频文案提取工作流 */
  AUDIO_TEXT_EXTRACTION = '7511265935560441891',
  
  /** 音频文案提取工作流(备用) */
  AUDIO_TEXT_EXTRACTION_ALT = '7511265128399749131',
  
  /** 材料处理工作流 */
  MATERIAL_PROCESSING = '7510518588304031756',
  
  /** 问答处理工作流 */
  QA_PROCESSING = '7507992502454452224',
  
  /** 人物处理工作流 */
  HUMAN_PROCESSING = '7510520073611624484',
  
  /** 账号定位分析工作流 */
  ACCOUNT_POSITIONING = '7517285166243512346',
}

/**
 * 工作流参数接口定义
 */
export interface WorkflowParams {
  [key: string]: any;
}

/**
 * 视频内容生成参数
 */
export interface VideoContentParams extends WorkflowParams {
  /** 账号定位 */
  account_positioning: string;
  /** 知识库 */
  knowledge: string;
  /** 问答数据 */
  qa: any[];
  /** 项目名称 */
  project_name: string;
  /** 人物数据 */
  human: any[];
  /** 基准数据列表 */
  benchmarking_list: any[];
  /** 热点数据 */
  hot: any;
  /** 限制数量 */
  limit: string;
  /** 日期 */
  date: string;
}

/**
 * 视频文件生成参数
 */
export interface VideoFileParams extends WorkflowParams {
  /** 分镜头数据 */
  segment: any[];
  /** 商户ID */
  merchant_id: string;
  /** 人物ID */
  human_id: string;
}

/**
 * 热点获取参数
 */
export interface HotContentParams extends WorkflowParams {
  /** 商户ID */
  merchant_id: string;
  /** 输入文本 */
  input: string;
  /** 日期 */
  date: string;
}

/**
 * 项目数据操作参数
 */
export interface ProjectDataParams extends WorkflowParams {
  /** 项目ID */
  project_id: string;
  /** 项目名称 */
  name?: string;
  /** 抖音账号名称 */
  project_name?: string;
  /** 知识库 */
  knowledge?: string;
  /** 账号定位 */
  account_positioning?: string;
}

/**
 * 音频文案提取参数
 */
export interface AudioTextParams extends WorkflowParams {
  /** 音频输入(URL或其他) */
  input: string;
}

/**
 * 账号定位分析参数
 */
export interface AccountPositioningParams extends WorkflowParams {
  /** IP账号ID */
  ip_id: string;
  /** 视频数据 */
  videos?: any[];
}

/**
 * Coze工作流管理器
 * 统一管理所有coze工作流的调用，提供类型安全的接口
 */
@Injectable()
export class CozeWorkflowManager {
  private readonly logger = new Logger(CozeWorkflowManager.name);

  /**
   * 执行工作流的通用方法
   * @param workflowId 工作流ID
   * @param params 参数对象
   * @param description 工作流描述(用于日志)
   * @returns 工作流执行结果
   */
  private async executeWorkflow(
    workflowId: CozeWorkflowId,
    params: WorkflowParams,
    description?: string,
  ): Promise<any> {
    this.logger.log(`开始执行工作流: ${description || workflowId}`, {
      workflowId,
      params: Object.keys(params),
    });

    try {
      const result = await runWorkflow(workflowId, params);
      this.logger.log(`工作流执行成功: ${description || workflowId}`);
      return result;
    } catch (error) {
      this.logger.error(`工作流执行失败: ${description || workflowId}`, {
        error: error.message,
        workflowId,
        params: Object.keys(params),
      });
      throw error;
    }
  }

  /**
   * 生成视频内容
   * @param params 视频内容生成参数
   * @returns 生成的视频内容数组
   */
  async generateVideoContent(params: VideoContentParams): Promise<any[]> {
    return this.executeWorkflow(
      CozeWorkflowId.VIDEO_CONTENT_GENERATION,
      params,
      '视频内容生成',
    );
  }

  /**
   * 生成视频文件
   * @param params 视频文件生成参数
   * @returns 视频文件URL
   */
  async generateVideoFile(params: VideoFileParams): Promise<string> {
    return this.executeWorkflow(
      CozeWorkflowId.VIDEO_FILE_GENERATION,
      params,
      '视频文件生成',
    );
  }

  /**
   * 获取热点内容
   * @param params 热点获取参数
   * @returns 热点内容数组
   */
  async fetchHotContent(params: HotContentParams): Promise<any[]> {
    return this.executeWorkflow(
      CozeWorkflowId.HOT_CONTENT_FETCH,
      params,
      '热点内容获取',
    );
  }

  /**
   * 创建项目数据
   * @param params 项目数据参数
   * @returns 创建结果
   */
  async createProjectData(params: ProjectDataParams): Promise<any> {
    return this.executeWorkflow(
      CozeWorkflowId.PROJECT_DATA_CREATE,
      params,
      '项目数据创建',
    );
  }

  /**
   * 编辑项目数据
   * @param params 项目数据参数
   * @returns 编辑结果
   */
  async editProjectData(params: ProjectDataParams): Promise<any> {
    return this.executeWorkflow(
      CozeWorkflowId.PROJECT_DATA_EDIT,
      params,
      '项目数据编辑',
    );
  }

  /**
   * 删除项目数据
   * @param projectId 项目ID
   * @returns 删除结果
   */
  async deleteProjectData(projectId: string): Promise<any> {
    return this.executeWorkflow(
      CozeWorkflowId.PROJECT_DATA_DELETE,
      { project_id: projectId },
      '项目数据删除',
    );
  }

  /**
   * 提取音频文案
   * @param params 音频文案提取参数
   * @param useAlternative 是否使用备用工作流
   * @returns 提取的文案内容
   */
  async extractAudioText(
    params: AudioTextParams,
    useAlternative = false,
  ): Promise<string> {
    const workflowId = useAlternative
      ? CozeWorkflowId.AUDIO_TEXT_EXTRACTION_ALT
      : CozeWorkflowId.AUDIO_TEXT_EXTRACTION;

    const result = await this.executeWorkflow(
      workflowId,
      params,
      '音频文案提取',
    );

    // 处理返回结果，统一格式
    if (typeof result === 'string') {
      return result;
    } else if (result && result.text) {
      return result.text;
    } else if (result && Array.isArray(result) && result.length > 0) {
      return result[0].text || result[0].result || '无法识别';
    }

    return '无法识别';
  }

  /**
   * 处理材料
   * @returns 材料处理结果
   */
  async processMaterial(): Promise<any> {
    return this.executeWorkflow(
      CozeWorkflowId.MATERIAL_PROCESSING,
      {},
      '材料处理',
    );
  }

  /**
   * 处理问答
   * @param params 问答处理参数
   * @returns 问答处理结果
   */
  async processQA(params: WorkflowParams): Promise<any> {
    return this.executeWorkflow(
      CozeWorkflowId.QA_PROCESSING,
      params,
      '问答处理',
    );
  }

  /**
   * 处理人物
   * @returns 人物处理结果
   */
  async processHuman(): Promise<any> {
    return this.executeWorkflow(
      CozeWorkflowId.HUMAN_PROCESSING,
      {},
      '人物处理',
    );
  }

  /**
   * 分析账号定位
   * @param params 账号定位参数
   * @returns 账号定位分析结果
   */
  async analyzeAccountPositioning(
    params: AccountPositioningParams,
  ): Promise<any> {
    return this.executeWorkflow(
      CozeWorkflowId.ACCOUNT_POSITIONING,
      params,
      '账号定位分析',
    );
  }

  /**
   * 自定义工作流执行
   * 用于执行未预定义的工作流或新增的工作流
   * @param workflowId 工作流ID
   * @param params 参数对象
   * @param description 工作流描述
   * @returns 工作流执行结果
   */
  async executeCustomWorkflow(
    workflowId: string,
    params: WorkflowParams,
    description?: string,
  ): Promise<any> {
    this.logger.log(`执行自定义工作流: ${description || workflowId}`, {
      workflowId,
      params: Object.keys(params),
    });

    try {
      const result = await runWorkflow(workflowId, params);
      this.logger.log(`自定义工作流执行成功: ${description || workflowId}`);
      return result;
    } catch (error) {
      this.logger.error(`自定义工作流执行失败: ${description || workflowId}`, {
        error: error.message,
        workflowId,
        params: Object.keys(params),
      });
      throw error;
    }
  }
}