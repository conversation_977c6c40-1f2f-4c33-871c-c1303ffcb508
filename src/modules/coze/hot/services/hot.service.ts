import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CommonService } from '../../../../utils/common.service';
import { Hot } from '../entities/hot.entity';
import { RemoveReqDto } from 'src/utils/remove.req.dto';
import { ZtBaseResDto } from 'src/utils/baseRes.dto';
import { HotResDto } from '../dto/res/hot.res.dto';
import { CozeWorkflowManager } from '../../coze-workflow.manager';
import { HotGetListReqDto } from '../dto/req/hot.getList.req.dto';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { reqUser } from '../../../../utils/nameSpace';

@Injectable()
export class HotService extends CommonService {
  constructor(
    @InjectRepository(Hot)
    private readonly hotRepository: Repository<Hot>,
    @Inject(REQUEST) request: Request,
    private cozeWorkflowManager: CozeWorkflowManager,
  ) {
    super(request);
  }

  // Hot CRUD
  async createHot(hot: Hot | Hot[]): Promise<void> {
    if (Array.isArray(hot)) {
      const entities = this.hotRepository.create(hot);
      await this.hotRepository.save(entities);
      return;
    }
    return this.createEntity(this.hotRepository, hot, 'Hot');
  }

  async updateHot(id: string, hot: Partial<Hot>): Promise<void> {
    return this.updateEntity(this.hotRepository, id, hot, 'Hot');
  }

  async deleteHot(params: RemoveReqDto): Promise<void> {
    return this.deleteEntity(this.hotRepository, params, 'Hot');
  }

  async findHot(filter: Partial<Hot>): Promise<ZtBaseResDto> {
    return this.findWithPagination(this.hotRepository, filter, HotResDto);
  }

  async getHotList(filter: Partial<HotGetListReqDto>): Promise<any> {
    const res = await this.cozeWorkflowManager.fetchHotContent({
      merchant_id: filter.merchantId,
      input: filter.text,
      date: filter.date,
    });
    if (res && res.length > 0) {
      const arr = [];
      for (const item of res) {
        const obj = {
          date: filter.date,
          merchantId: filter.merchantId,
          text: item.text,
          title: item.title,
          type: item.type,
        };
        arr.push(obj);
      }

      await this.createHot(arr);
    }
    return res;
  }

  async findOne(param: { merchantId: string; date: string }) {
    const { merchantId, date } = param;
    return this.hotRepository.find({
      where: { merchantId, date },
    });
  }
}
