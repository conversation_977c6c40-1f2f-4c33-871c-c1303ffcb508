import { Body, Controller, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { HotService } from '../services/hot.service';
import { Hot } from '../entities/hot.entity';
import { HotReqDto } from '../dto/req/hot.req.dto';
import { RemoveReqDto } from '../../../../utils/remove.req.dto';
import { HotGetListReqDto } from '../dto/req/hot.getList.req.dto';

@ApiTags('coze-hot')
@Controller('coze/hot')
export class HotController {
  constructor(private readonly hotService: HotService) {}

  // Hot CRUD
  @Post('create')
  @ApiOperation({ summary: '创建热点' })
  @ApiResponse({ status: 201, description: '热点创建成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  async createHot(@Body() hot: Hot) {
    return this.hotService.createHot(hot);
  }

  @Post('update')
  @ApiOperation({ summary: '更新热点' })
  @ApiResponse({ status: 200, description: '热点更新成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  @ApiResponse({ status: 404, description: '热点不存在' })
  async updateHot(@Body() hot: HotReqDto) {
    return this.hotService.updateHot(hot.id, hot);
  }

  @Post('remove')
  @ApiOperation({ summary: '删除热点' })
  @ApiResponse({ status: 200, description: '热点删除成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  @ApiResponse({ status: 404, description: '热点不存在' })
  async deleteHot(@Body() params: RemoveReqDto) {
    return this.hotService.deleteHot(params);
  }

  @Post('find')
  @ApiOperation({ summary: '查询热点' })
  @ApiResponse({ status: 200, description: '热点查询成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  async findHot(@Body() filter: Partial<Hot>) {
    return this.hotService.findHot(filter);
  }

  @Post('getHotList')
  @ApiOperation({ summary: '获取热点列表' })
  @ApiResponse({ status: 200, description: '获取热点列表成功' })
  @ApiResponse({ status: 400, description: '请求参数无效' })
  async getHotList(@Body() filter: Partial<HotGetListReqDto>) {
    return this.hotService.getHotList(filter);
  }
}
