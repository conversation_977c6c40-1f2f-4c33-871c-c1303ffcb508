import { Column, Entity } from 'typeorm';
import { ZtBaseEntity } from '../../../../utils/base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

/**
 * 热点实体
 */
@Entity()
export class Hot extends ZtBaseEntity {
  /**
   * 标题
   */
  @ApiProperty({ example: '热点标题', description: '标题' })
  @Column()
  @IsNotEmpty()
  title: string;

  /**
   * 内容
   */
  @ApiProperty({ example: '热点内容详情', description: '内容' })
  @Column({ length: 2000 })
  @IsNotEmpty()
  text: string;

  /**
   * 日期
   */
  @ApiProperty({ example: '2024-03-20', description: '日期' })
  @Column()
  @IsNotEmpty()
  date: string;

  /**
   * 来源
   */
  @ApiProperty({ example: '抖音', description: '来源，抖音或者小红书之类的' })
  @Column()
  @IsNotEmpty()
  type: string;

  /**
   * 商户ID
   */
  @ApiProperty({ example: 'MERCHANT001', description: '商户id' })
  @Column()
  @IsNotEmpty()
  merchantId: string;
}
