// src/modules/coze/dto/req/hot.req.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { Column } from 'typeorm';
import { ZtBaseReqDto } from '../../../../../utils/baseReq.dto';

export class HotGetListReqDto extends ZtBaseReqDto {
  /**
   * 内容
   */
  @ApiProperty({ example: '热点内容详情', description: '内容' })
  @Column()
  @IsNotEmpty()
  @IsString()
  text: string;

  /**
   * 日期
   */
  @ApiProperty({ example: '2024-03-20', description: '日期' })
  @IsNotEmpty()
  @IsString()
  date: string;

  /**
   * 商户ID
   */
  @ApiProperty({ example: 'MERCHANT001', description: '商户id' })
  @Column()
  @IsNotEmpty()
  @IsString()
  merchantId: string;
}
