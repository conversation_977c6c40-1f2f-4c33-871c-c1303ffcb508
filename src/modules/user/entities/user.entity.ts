// entities/user.entity.ts
import { Column, Entity, JoinTable, ManyToMany } from 'typeorm';
import { Role } from './role.entity';
import { ZtBaseEntity } from '../../../utils/base.entity';

/**
 * 用户实体类
 * 
 * 该实体用于存储和管理系统中的用户信息，包括基本资料、身份认证信息以及权限管理。
 * 用户是系统的主要操作者，通过角色关联获得相应的权限。
 * 
 * 实体与Role（角色）存在多对多的关联关系，一个用户可以拥有多个角色，一个角色也可以被多个用户拥有。
 * 继承自ZtBaseEntity，包含基础字段如id、createdAt、updatedAt等。
 */
@Entity()
export class User extends ZtBaseEntity {
  /**
   * 用户昵称
   * 
   * 用户在系统中显示的名称，可用于UI展示和搜索
   * 该字段必须提供且不能为空
   */
  @Column()
  nickname: string;

  /**
   * 用户状态
   * 
   * 表示用户账号是否启用，值为字符串类型
   * '1' - 启用状态
   * '0' - 禁用状态
   * 默认值为'1'，表示新创建的用户默认为启用状态
   */
  @Column({ default: '1' })
  status: string;

  /**
   * 用户头像
   * 
   * 存储用户头像图片的URL地址
   * 该字段可以为空，用于在界面上显示用户的个人头像
   */
  @Column({ nullable: true })
  avatar?: string;

  /**
   * 用户登录名
   * 
   * 用户在系统登录时使用的唯一标识符
   * 通常使用字母、数字和特殊字符的组合
   * 该字段必须提供且不能为空，建议在应用层确保其唯一性
   */
  @Column()
  username: string;

  /**
   * 用户密码
   * 
   * 存储用户的登录密码
   * 应当以加密形式存储，而不是明文
   * 该字段必须提供且不能为空
   */
  @Column()
  password: string;

  /**
   * 密码加密方法（目前已注释）
   * 
   * TypeORM生命周期钩子，用于在插入和更新前自动加密密码
   * 注意：由于前端传递加密后密码的情况，此功能已被注释
   * 建议在服务层处理密码加密逻辑，以灵活应对不同场景
   */
  // @BeforeUpdate()
  // @BeforeInsert()
  // encryptPassword() {
  //   console.log(this.password);
  //   if (this.password) {
  //     this.password = encrypt(this.password, config().password.secret);
  //   }
  // }

  /**
   * 用户角色关联
   * 
   * 与Role实体的多对多关系，定义用户拥有的所有角色
   * 通过角色间接定义了用户的权限范围
   * 使用@JoinTable()创建连接表实现多对多关系
   */
  @ManyToMany(() => Role, (role) => role.users)
  @JoinTable()
  roles: Role[];

  /**
   * 联系电话
   * 
   * 用户的手机号码或固定电话
   * 该字段可以为空，用于联系和通知用户
   * 建议在应用层进行格式验证
   */
  @Column({ nullable: true })
  tel?: string;

  /**
   * 电子邮箱
   * 
   * 用户的电子邮件地址
   * 该字段可以为空，用于系统通知和密码重置等功能
   * 建议在应用层进行格式验证
   */
  @Column({ nullable: true })
  email?: string;
}
