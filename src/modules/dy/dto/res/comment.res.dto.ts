import { ApiProperty } from '@nestjs/swagger';

export class CommentUserDto {
  @ApiProperty({ description: '用户ID' })
  uid: string;

  @ApiProperty({ description: '用户安全ID' })
  sec_uid: string;

  @ApiProperty({ description: '用户昵称' })
  nickname: string;

  @ApiProperty({ description: '用户头像' })
  avatar_url: string;

  @ApiProperty({ description: '用户IP属地', required: false })
  ip_location?: string;
}

export class CommentItemDto {
  @ApiProperty({ description: '评论ID' })
  cid: string;

  @ApiProperty({ description: '评论内容' })
  text: string;

  @ApiProperty({ description: '创建时间', example: 1617364821 })
  create_time: number;

  @ApiProperty({ description: '点赞数量' })
  digg_count: number;

  @ApiProperty({ description: '回复数量' })
  reply_count: number;

  @ApiProperty({ description: '评论用户信息' })
  user: CommentUserDto;

  @ApiProperty({ description: '是否置顶', required: false })
  is_top?: boolean;
  
  @ApiProperty({ description: '回复评论列表', required: false, type: [CommentItemDto] })
  reply_comments?: CommentItemDto[];
}

export class CommentResDto {
  @ApiProperty({ description: '评论列表', type: [CommentItemDto] })
  comments: CommentItemDto[];

  @ApiProperty({ description: '页码游标' })
  cursor: string;

  @ApiProperty({ description: '是否有更多数据' })
  has_more: boolean;

  @ApiProperty({ description: '总条数' })
  total: number;

  @ApiProperty({ description: '状态码', example: 0 })
  status_code: number;

  @ApiProperty({ description: '状态消息', example: '', required: false })
  status_msg: string;
} 