import { ApiProperty } from '@nestjs/swagger';

/**
 * 用户信息DTO
 */
export class UserInfoDto {
  @ApiProperty({ description: '用户ID' })
  uid: string;

  @ApiProperty({ description: '用户安全ID' })
  sec_uid: string;

  @ApiProperty({ description: '用户昵称' })
  nickname: string;

  @ApiProperty({ description: '用户签名', required: false })
  signature: string;

  @ApiProperty({ description: '用户头像URL', required: false })
  avatar_url: string;

  @ApiProperty({ description: '关注数量' })
  following_count: number;

  @ApiProperty({ description: '粉丝数量' })
  follower_count: number;

  @ApiProperty({ description: '获赞数量' })
  total_favorited: number;

  @ApiProperty({ description: '作品数量' })
  aweme_count: number;

  @ApiProperty({ description: 'IP所在地', required: false })
  ip_location?: string;

  @ApiProperty({ description: '省份', required: false })
  province?: string;

  @ApiProperty({ description: '城市', required: false })
  city?: string;

  @ApiProperty({ description: '短ID', required: false })
  short_id?: string;
}

/**
 * 作者信息DTO
 */
export class AuthorDto {
  @ApiProperty({ description: '用户ID' })
  uid: string;

  @ApiProperty({ description: '用户安全ID' })
  sec_uid: string;

  @ApiProperty({ description: '用户昵称' })
  nickname: string;
}

/**
 * 统计信息DTO
 */
export class StatisticsDto {
  @ApiProperty({ description: '点赞数量' })
  digg_count: number;

  @ApiProperty({ description: '评论数量' })
  comment_count: number;

  @ApiProperty({ description: '收藏数量' })
  collect_count: number;

  @ApiProperty({ description: '分享数量' })
  share_count: number;
}

/**
 * 账号内容项DTO
 */
export class AccountItemDto {
  @ApiProperty({ description: '内容ID' })
  id: string;

  @ApiProperty({ description: '内容标题' })
  title: string;

  @ApiProperty({ description: '内容描述' })
  desc: string;

  @ApiProperty({ description: '音频URL', required: false })
  audioUrl?: string; // 使用可选属性

  @ApiProperty({ description: '视频时长（秒）' })
  duration: number;

  // 视频封面
  @ApiProperty({ description: '视频封面URL', required: false })
  cover_url?: string;

  @ApiProperty({ description: '创建时间戳' })
  create_time: number;

  @ApiProperty({ description: '视频/图片URL' })
  video_url: string;

  @ApiProperty({ description: '作者信息' })
  author: AuthorDto;

  @ApiProperty({ description: '统计信息' })
  statistics: StatisticsDto;

  @ApiProperty({ description: '内容类型', enum: ['video', 'image'] })
  type: string;
}

/**
 * 账号响应DTO
 */
export class AccountResDto {
  @ApiProperty({
    description: '用户信息',
    type: UserInfoDto,
    required: false,
    nullable: true,
  })
  user_info: UserInfoDto | null;

  @ApiProperty({ description: '内容列表', type: [AccountItemDto] })
  items: AccountItemDto[];

  @ApiProperty({ description: '分页游标' })
  cursor: string;

  @ApiProperty({ description: '是否有更多数据' })
  has_more: boolean;

  @ApiProperty({ description: '状态码', example: 0 })
  status_code: number;

  @ApiProperty({ description: '状态消息', example: '' })
  status_msg: string;
}
