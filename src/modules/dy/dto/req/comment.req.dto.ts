import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsNumber } from 'class-validator';

export class CommentReqDto {
  @ApiProperty({
    description: '作品ID',
    example: '7337955959296135456',
  })
  @IsNotEmpty({ message: '作品ID不能为空' })
  @IsString()
  aweme_id: string;

  @ApiProperty({
    description: '页码游标',
    example: '0',
    required: false,
  })
  @IsOptional()
  @IsString()
  cursor?: string = '0';

  @ApiProperty({
    description: '每页评论数量',
    example: 20,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  count?: number = 20;
  
  @ApiProperty({
    description: '评论类型 (0:普通评论)',
    example: '0',
    required: false,
  })
  @IsOptional()
  @IsString()
  item_type?: string = '0';
} 