import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsIn,
  IsDateString,
  IsEnum,
} from 'class-validator';

/**
 * 账号内容类型
 */
export enum AccountTab {
  /** 发布内容 */
  POST = 'post',
  /** 喜欢内容 */
  LIKE = 'like',
}

/**
 * 抖音账号请求参数
 */
export class AccountReqDto {
  @ApiProperty({
    description: '抖音用户主页URL',
    example: 'https://www.douyin.com/user/MS4wLjABAAAAYjRFJ6EU-FDy7Srq6OkJxbL5lA4kTLepDjyJw37wU_gr7G5COGolmom2xlC0a-Oe',
    required: true,
  })
  @IsNotEmpty({ message: 'user_url不能为空' })
  @IsString()
  user_url: string;

  @ApiPropertyOptional({
    description: '账号内容类型: post(发布), like(喜欢)',
    enum: AccountTab,
    default: AccountTab.POST,
  })
  @IsOptional()
  @IsEnum(AccountTab, { message: 'tab必须是post或like' })
  tab?: string;

  @ApiPropertyOptional({
    description: '分页游标',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @IsNumber({}, { message: 'cursor必须是数字' })
  cursor?: number;

  @ApiPropertyOptional({
    description: '每页数量',
    example: 18,
    default: 18,
  })
  @IsOptional()
  @IsNumber({}, { message: 'count必须是数字' })
  count?: number;

  @ApiPropertyOptional({
    description: '最早日期(YYYY-MM-DD或天数)',
    example: '2023-01-01',
  })
  @IsOptional()
  earliest?: string | number;

  @ApiPropertyOptional({
    description: '最晚日期(YYYY-MM-DD或天数)',
    example: '2023-12-31',
  })
  @IsOptional()
  latest?: string | number;

  @ApiPropertyOptional({
    description: '最大页数(仅对like页有效)',
    example: 5,
  })
  @IsOptional()
  @IsNumber({}, { message: 'pages必须是数字' })
  pages?: number;
} 