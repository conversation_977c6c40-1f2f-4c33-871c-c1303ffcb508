/**
 * 抖音API测试脚本
 * 用于验证API优化效果
 */

import { APIClient } from '../utils/api';
import { DouyinParamGenerator } from '../utils/param-generator';
import { DEFAULT_API_CONFIG } from '../config/api-config';

/**
 * 测试API客户端
 */
async function testAPIClient() {
  console.log('🚀 开始测试抖音API客户端...\n');

  // 创建API客户端
  const apiClient = new APIClient();
  
  // 测试用户ID（示例）
  const testSecUserId = 'MS4wLjABAAAAKIVBqjLSqIpLYKVazXQznWSMw-63auN4T1dVX30Ou8XXt-BMRKFGPT9o-fsCbJaS';

  try {
    // 测试1: 获取用户信息
    console.log('📋 测试1: 获取用户信息');
    console.log('用户ID:', testSecUserId);
    
    const userInfo = await apiClient.getUserInfo(testSecUserId);
    console.log('✅ 用户信息获取成功');
    console.log('用户昵称:', userInfo.user?.nickname || '未知');
    console.log('粉丝数:', userInfo.user?.follower_count || 0);
    console.log('作品数:', userInfo.user?.aweme_count || 0);
    console.log('');

    // 测试2: 获取用户作品
    console.log('📹 测试2: 获取用户作品');
    
    const userPosts = await apiClient.getUserPosts(testSecUserId, 0, 5);
    console.log('✅ 用户作品获取成功');
    console.log('作品数量:', userPosts.aweme_list?.length || 0);
    console.log('是否有更多:', userPosts.has_more);
    console.log('下一页游标:', userPosts.max_cursor);
    
    if (userPosts.aweme_list && userPosts.aweme_list.length > 0) {
      const firstPost = userPosts.aweme_list[0];
      console.log('第一个作品描述:', firstPost.desc || '无描述');
      console.log('点赞数:', firstPost.statistics?.digg_count || 0);
    }
    console.log('');

    // 测试3: 获取用户喜欢的作品
    console.log('❤️ 测试3: 获取用户喜欢的作品');
    
    const userLikes = await apiClient.getUserLikes(testSecUserId, 0, 5);
    console.log('✅ 用户喜欢作品获取成功');
    console.log('喜欢作品数量:', userLikes.aweme_list?.length || 0);
    console.log('');

    console.log('🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

/**
 * 测试参数生成器
 */
function testParamGenerator() {
  console.log('🔧 测试参数生成器...\n');

  const generator = new DouyinParamGenerator();

  console.log('WebID:', generator.getWebId());
  console.log('UIFID:', generator.getUifid());
  console.log('VerifyFp:', generator.generateVerifyFp());
  console.log('MsToken:', generator.generateMsToken());
  console.log('A-Bogus:', generator.generateABogus());
  console.log('');
}

/**
 * 测试配置
 */
function testConfig() {
  console.log('⚙️ 测试配置...\n');

  console.log('默认配置:');
  console.log('AID:', DEFAULT_API_CONFIG.aid);
  console.log('版本:', DEFAULT_API_CONFIG.version_name);
  console.log('浏览器:', DEFAULT_API_CONFIG.browser_name, DEFAULT_API_CONFIG.browser_version);
  console.log('CPU核心数:', DEFAULT_API_CONFIG.cpu_core_num);
  console.log('设备内存:', DEFAULT_API_CONFIG.device_memory, 'GB');
  console.log('屏幕分辨率:', `${DEFAULT_API_CONFIG.screen_width}x${DEFAULT_API_CONFIG.screen_height}`);
  console.log('');
}

/**
 * 对比参数差异
 */
function compareWithBrowserRequest() {
  console.log('🔍 对比浏览器请求参数...\n');

  const browserParams = {
    aid: '6383',
    version_code: '290100',
    version_name: '29.1.0',
    browser_version: '122.0.6261.95',
    cpu_core_num: 12,
    device_memory: 8,
    screen_width: 2560,
    screen_height: 1440,
  };

  const ourParams = {
    aid: DEFAULT_API_CONFIG.aid,
    version_code: DEFAULT_API_CONFIG.version_code,
    version_name: DEFAULT_API_CONFIG.version_name,
    browser_version: DEFAULT_API_CONFIG.browser_version,
    cpu_core_num: DEFAULT_API_CONFIG.cpu_core_num,
    device_memory: DEFAULT_API_CONFIG.device_memory,
    screen_width: DEFAULT_API_CONFIG.screen_width,
    screen_height: DEFAULT_API_CONFIG.screen_height,
  };

  console.log('参数对比结果:');
  Object.keys(browserParams).forEach(key => {
    const browserValue = browserParams[key];
    const ourValue = ourParams[key];
    const match = browserValue === ourValue;
    console.log(`${key}: ${match ? '✅' : '❌'} 浏览器=${browserValue}, 我们=${ourValue}`);
  });
  console.log('');
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🧪 抖音API测试套件\n');
  console.log('=' * 50);

  // 测试配置
  testConfig();

  // 测试参数生成器
  testParamGenerator();

  // 对比参数
  compareWithBrowserRequest();

  // 测试API客户端（需要有效的网络连接）
  const shouldTestAPI = process.env.TEST_API === 'true';
  if (shouldTestAPI) {
    await testAPIClient();
  } else {
    console.log('⚠️ 跳过API测试（设置 TEST_API=true 启用）');
  }

  console.log('=' * 50);
  console.log('测试完成！');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTests().catch(console.error);
}

export {
  testAPIClient,
  testParamGenerator,
  testConfig,
  compareWithBrowserRequest,
  runTests
};
