import { Injectable, Logger } from '@nestjs/common';
import { AccountReqDto } from '../dto/req/account.req.dto';
import { AccountItemDto, AccountResDto, UserInfoDto } from '../dto/res/account.res.dto';
import { APIClient } from '../utils/api';
import { checkDate, getUserSecId } from '../utils';
import { CommentInfo, ContentItem, ContentListResponse } from '../types';
import { ConfigService } from '@nestjs/config';
import { WhisperService } from '../../../utils/whisper';
import { AudioReqDto } from '../dto/req/audio.req.dto';
import { AudioResDto } from '../dto/res/audio.res.dto';
import { CommentReqDto } from '../dto/req/comment.req.dto';
import { CommentItemDto, CommentResDto, CommentUserDto } from '../dto/res/comment.res.dto';

@Injectable()
export class DyService {
  private readonly logger = new Logger(DyService.name);
  private readonly apiClient: APIClient;
  private whisperService: WhisperService;

  constructor(private configService: ConfigService) {
    // 获取抖音配置
    const douyinConfig = this.configService.get('douyin') || {};

    // 初始化API客户端，从配置中读取代理设置
    const proxy = douyinConfig.PROXY_URL || '';
    this.apiClient = new APIClient(proxy);
    this.whisperService = new WhisperService();

    // 输出配置信息
    this.logger.log(
      `抖音API客户端初始化完成，模拟评论: ${douyinConfig.USE_MOCK_COMMENTS}`,
    );
  }

  /**
   * 获取抖音账号数据
   * @param accountDto 账号请求参数
   * @returns 账号数据
   */
  async getAccountData(accountDto: AccountReqDto): Promise<AccountResDto> {
    try {
      // 获取参数
      const {
        user_url,
        tab = 'post',
        cursor = 0,
        count = 18,
        earliest,
        latest,
        pages,
      } = accountDto;

      // 从URL获取sec_user_id
      let sec_user_id: string;
      try {
        sec_user_id = await getUserSecId(user_url);
        this.logger.log(`从URL获取sec_user_id成功: ${sec_user_id}`);
      } catch (error) {
        throw new Error(`从URL获取sec_user_id失败: ${error.message}`);
      }

      // 获取用户信息
      const userInfo = await this.getUserInfo(sec_user_id);

      // 获取用户内容列表
      const {
        items,
        cursor: nextCursor,
        has_more,
      } = await this.getUserContents(
        sec_user_id,
        tab,
        cursor,
        count,
        earliest,
        latest,
        pages,
      );

      // 返回结果
      return {
        user_info: userInfo,
        items,
        cursor: nextCursor.toString(),
        has_more,
        status_code: 0,
        status_msg: '',
      };
    } catch (error) {
      this.logger.error(`获取账号数据失败: ${error.message}`, error.stack);
      return {
        user_info: null,
        items: [],
        cursor: '0',
        has_more: false,
        status_code: 500,
        status_msg: error.message,
      };
    }
  }

  /**
   * 获取用户信息
   * @param secUserId 用户安全ID
   * @returns 用户信息
   */
  async getUserInfo(secUserId: string): Promise<UserInfoDto> {
    try {
      const response = await this.apiClient.getUserInfo(secUserId);

      // 检查响应数据
      if (!response?.user) {
        throw new Error('获取用户信息失败：返回数据格式错误');
      }

      const user = response.user;

      // 转换为DTO格式，添加空值和类型检查
      return {
        uid: user.uid?.toString() || '',
        sec_uid: user.sec_uid || '',
        nickname: user.nickname || '未知用户',
        signature: user.signature || '',
        avatar_url: Array.isArray(user.avatar_larger?.url_list)
          ? user.avatar_larger.url_list[0]
          : user.avatar_medium?.url_list?.[0] || '', // 备选头像
        following_count: Number(user.following_count) || 0,
        follower_count: Number(user.follower_count) || 0,
        total_favorited: Number(user.total_favorited) || 0,
        aweme_count: Number(user.aweme_count) || 0,
        // 新增可选字段
        ip_location: user.ip_location || '',
        province: user.province || '',
        city: user.city || '',
        short_id: user.short_id || '',
      };
    } catch (error) {
      this.logger.error(
        `获取用户信息失败: ${error.message}`,
        error.stack,
        DyService.name,
      );
      throw new Error(`获取用户信息失败: ${error.message}`);
    }
  }

  /**
   * 获取用户内容列表
   * @param sec_user_id 用户安全ID
   * @param tab 内容类型
   * @param cursor 分页游标
   * @param count 每页数量
   * @param earliest 最早日期
   * @param latest 最晚日期
   * @param pages 最大页数
   * @returns 内容列表
   */
  async getUserContents(
    sec_user_id: string,
    tab: string,
    cursor: number,
    count: number,
    earliest?: string | number,
    latest?: string | number,
    pages?: number,
  ): Promise<{ items: AccountItemDto[]; cursor: number; has_more: boolean }> {
    // 结果数组
    const allItems: AccountItemDto[] = [];

    // 处理日期范围
    const earliestDate = earliest
      ? checkDate(earliest, new Date('2016-09-20'), '最早日期')
      : null;
    const latestDate = latest
      ? checkDate(latest, new Date(), '最晚日期')
      : null;

    // 结果集
    let currentCursor = cursor;
    let hasMore = true;
    let currentPage = 1;
    const maxPages = pages || 10; // 默认最多获取10页

    // 重试相关
    let retryCount = 0;
    const maxRetryCount = 3;

    // 循环获取所有页
    while (hasMore && currentPage <= maxPages) {
      try {
        // 根据类型获取不同的内容
        let response: ContentListResponse;
        if (tab === 'like') {
          response = await this.apiClient.getUserLikes(
            sec_user_id,
            currentCursor,
            count,
          );
        } else {
          response = await this.apiClient.getUserPosts(
            sec_user_id,
            currentCursor,
            count,
          );
        }

        // 检查响应
        if (!response || !response.aweme_list) {
          if (retryCount < maxRetryCount) {
            retryCount++;
            this.logger.log(`重试第 ${retryCount}/${maxRetryCount} 次`);
            await new Promise((resolve) => setTimeout(resolve, 2000)); // 增加等待时间
            continue;
          } else {
            throw new Error('获取内容列表失败：返回数据格式错误');
          }
        }

        // 重置重试计数
        retryCount = 0;

        // 过滤并处理内容项
        const filteredItems = this.filterContentItems(
          response.aweme_list || [],
          earliestDate,
          latestDate,
        );

        // 转换为DTO格式
        const dtoItems = filteredItems.map(this.mapContentItemToDto);
        allItems.push(...dtoItems);

        // 更新分页信息
        currentCursor = response.max_cursor;
        hasMore =
          response.has_more &&
          !this.shouldStopFetching(tab, earliestDate, currentCursor);

        currentPage += 1;

        this.logger.debug(
          `已获取第 ${currentPage} 页数据, 共 ${allItems.length} 条`,
        );

        // 如果需要提前结束，则跳出循环
        if (!hasMore) {
          break;
        }

        // 添加延迟避免频繁请求
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error) {
        // 处理请求错误
        if (retryCount < maxRetryCount) {
          retryCount++;
          this.logger.log(
            `请求失败，重试第 ${retryCount}/${maxRetryCount} 次: ${error.message}`,
          );
          await new Promise((resolve) => setTimeout(resolve, 2000)); // 增加等待时间
        } else {
          this.logger.error(
            `获取数据失败，已达到最大重试次数: ${error.message}`,
          );
          break;
        }
      }
    }

    return {
      items: allItems,
      cursor: currentCursor,
      has_more: hasMore,
    };
  }

  // 在 src/modules/dy/services/dy.service.ts 中添加
  async audioToText(audioDto: AudioReqDto): Promise<AudioResDto> {
    try {
      const text = await this.whisperService.transcribe(audioDto.audioUrl);
      return {
        text,
        status_code: 0,
        status_msg: '',
      };
    } catch (error) {
      this.logger.error(`音频转文字失败: ${error.message}`, error.stack);
      return {
        text: '',
        status_code: 500,
        status_msg: error.message,
      };
    }
  }

  /**
   * 获取作品评论
   * @param commentDto 评论请求参数
   * @returns 评论列表响应
   */
  async getComments(commentDto: CommentReqDto): Promise<CommentResDto> {
    try {
      const {
        aweme_id,
        cursor = '0',
        count = 20,
        item_type = '0',
      } = commentDto;

      this.logger.log(`开始获取作品[${aweme_id}]的评论数据，游标: ${cursor}`);

      const response = await this.apiClient.getComments(
        aweme_id,
        cursor,
        count,
        item_type,
      );

      // 处理可能返回为空的情况
      if (
        !response ||
        !response.comments ||
        !Array.isArray(response.comments)
      ) {
        this.logger.warn(`获取评论数据为空: ${JSON.stringify(response)}`);
        return {
          comments: [],
          cursor: cursor,
          has_more: false,
          total: 0,
          status_code: response?.status_code || 0,
          status_msg: response?.status_msg || '评论数据为空',
        };
      }

      // 转换评论列表
      const commentItems = response.comments.map((comment) =>
        this.mapCommentInfoToDto(comment),
      );

      this.logger.log(
        `成功获取作品[${aweme_id}]的评论数据: ${commentItems.length}条`,
      );

      return {
        comments: commentItems,
        cursor: response.cursor || '0',
        has_more: response.has_more || false,
        total: response.total || commentItems.length,
        status_code: 0,
        status_msg: '',
      };
    } catch (error) {
      this.logger.error(`获取评论数据失败: ${error.message}`, error.stack);
      return {
        comments: [],
        cursor: '0',
        has_more: false,
        total: 0,
        status_code: 500,
        status_msg: error.message,
      };
    }
  }

  /**
   * 判断是否应该停止获取数据
   * @param tab 内容类型
   * @param earliestDate 最早日期
   * @param cursor 当前游标
   * @returns 是否应该停止
   */
  private shouldStopFetching(
    tab: string,
    earliestDate: Date | null,
    cursor: number,
  ): boolean {
    // 如果不是喜欢页，并且有最早日期限制，判断当前游标时间是否早于最早限制
    if (tab !== 'like' && earliestDate) {
      const cursorDate = new Date(cursor * 1000);
      return cursorDate < earliestDate;
    }
    return false;
  }

  /**
   * 根据日期过滤内容项
   * @param items 内容项数组
   * @param earliest 最早日期
   * @param latest 最晚日期
   * @returns 过滤后的内容项数组
   */
  private filterContentItems(
    items: ContentItem[],
    earliest: Date | null,
    latest: Date | null,
  ): ContentItem[] {
    if (!items || items.length === 0) {
      return [];
    }

    let filteredItems = [...items];

    // 按最早时间过滤
    if (earliest) {
      const earliestTimestamp = earliest.getTime() / 1000;
      filteredItems = filteredItems.filter(
        (item) => item.create_time >= earliestTimestamp,
      );
    }

    // 按最晚时间过滤
    if (latest) {
      const latestTimestamp = latest.getTime() / 1000;
      filteredItems = filteredItems.filter(
        (item) => item.create_time <= latestTimestamp,
      );
    }

    return filteredItems;
  }

  /**
   * 将内容项转换为DTO
   * @param item 内容项
   * @returns DTO
   */
  private mapContentItemToDto(item: ContentItem): AccountItemDto {
    // 获取视频URL
    let videoUrl = '';
    if (item.video && item.video.play_addr && item.video.play_addr.url_list) {
      videoUrl =
        item.video.play_addr.url_list[2] || item.video.play_addr.url_list[0];
    } else if (item.video.play_addr.play_addr_265.url_list.length > 0) {
      videoUrl =
        item.video.play_addr.play_addr_265.url_list[2] ||
        item.video.play_addr.play_addr_265.url_list[0];
    }

    // 构建DTO
    return {
      title: item.item_title || item.desc || '无标题',
      id: item.aweme_id,
      desc: item.desc,
      create_time: item.create_time,
      video_url: videoUrl,
      audioUrl: item.music?.play_url?.url_list?.[0],
      cover_url: item.images?.[0]?.url_list?.[0],
      duration: item.video?.duration || 0,
      author: {
        uid: item.author.uid,
        sec_uid: item.author.sec_uid,
        nickname: item.author.nickname,
      },
      statistics: {
        digg_count: item.statistics.digg_count,
        comment_count: item.statistics.comment_count,
        collect_count: item.statistics.collect_count,
        share_count: item.statistics.share_count,
      },
      type: item.aweme_type === 2 ? 'image' : 'video',
    };
  }

  /**
   * 将评论信息映射为DTO
   * @param comment 原始评论信息
   * @returns 评论DTO
   */
  private mapCommentInfoToDto(comment: CommentInfo): CommentItemDto {
    // 将用户信息转换为DTO
    const userDto: CommentUserDto = {
      uid: comment.user.uid || '',
      sec_uid: comment.user.sec_uid || '',
      nickname: comment.user.nickname || '',
      avatar_url: comment.user.avatar_thumb?.url_list?.[0] || '',
      ip_location: comment.user.ip_location,
    };

    // 转换回复评论（如果存在）
    const replyComments = comment.reply_comment
      ? comment.reply_comment.map((reply) => ({
        cid: reply.cid || '',
        text: reply.text || '',
        create_time: reply.create_time || 0,
        digg_count: reply.digg_count || 0,
        reply_count: reply.reply_comment_total || 0,
        user: {
          uid: reply.user.uid || '',
          sec_uid: reply.user.sec_uid || '',
          nickname: reply.user.nickname || '',
          avatar_url: reply.user.avatar_thumb?.url_list?.[0] || '',
          ip_location: reply.user.ip_location,
        },
        is_top: reply.is_top || false,
      }))
      : [];

    // 返回评论DTO
    return {
      cid: comment.cid || '',
      text: comment.text || '',
      create_time: comment.create_time || 0,
      digg_count: comment.digg_count || 0,
      reply_count: comment.reply_comment_total || 0,
      user: userDto,
      is_top: comment.is_top || false,
      reply_comments: replyComments.length > 0 ? replyComments : undefined,
    };
  }
}
