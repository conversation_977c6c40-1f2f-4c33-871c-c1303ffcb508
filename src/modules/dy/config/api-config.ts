/**
 * 抖音API配置文件
 * 集中管理API请求的各种参数
 */

export interface DouyinApiConfig {
  // 基础参数
  aid: string;
  channel: string;
  device_platform: string;
  version_code: string;
  version_name: string;
  
  // 浏览器环境参数
  browser_name: string;
  browser_version: string;
  browser_language: string;
  browser_platform: string;
  engine_name: string;
  engine_version: string;
  
  // 系统环境参数
  os_name: string;
  os_version: string;
  platform: string;
  
  // 设备参数
  cpu_core_num: number;
  device_memory: number;
  screen_width: number;
  screen_height: number;
  
  // 网络参数
  downlink: number;
  effective_type: string;
  round_trip_time: number;
}

/**
 * 默认API配置
 * 基于最新的浏览器请求参数
 */
export const DEFAULT_API_CONFIG: DouyinApiConfig = {
  // 基础参数
  aid: '6383',
  channel: 'channel_pc_web',
  device_platform: 'webapp',
  version_code: '290100',
  version_name: '29.1.0',
  
  // 浏览器环境参数
  browser_name: 'Chrome',
  browser_version: '122.0.6261.95',
  browser_language: 'zh-CN',
  browser_platform: 'Win32',
  engine_name: 'Blink',
  engine_version: '122.0.6261.95',
  
  // 系统环境参数
  os_name: 'Windows',
  os_version: '10',
  platform: 'PC',
  
  // 设备参数
  cpu_core_num: 12,
  device_memory: 8,
  screen_width: 2560,
  screen_height: 1440,
  
  // 网络参数
  downlink: 7.1,
  effective_type: '4g',
  round_trip_time: 100,
};

/**
 * 请求头配置
 */
export const DEFAULT_HEADERS = {
  'accept': 'application/json, text/plain, */*',
  'accept-language': 'zh-CN,zh;q=0.9',
  'sec-ch-ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
};

/**
 * API端点配置
 */
export const API_ENDPOINTS = {
  USER_PROFILE: '/aweme/v1/web/user/profile/other/',
  USER_POSTS: '/aweme/v1/web/aweme/post/',
  USER_LIKES: '/aweme/v1/web/aweme/favorite/',
  COMMENTS: '/aweme/v1/web/comment/list/',
};

/**
 * 生成随机设备参数
 * 用于模拟不同的设备环境
 */
export function generateRandomDeviceConfig(): Partial<DouyinApiConfig> {
  const screenResolutions = [
    { width: 1920, height: 1080 },
    { width: 2560, height: 1440 },
    { width: 1366, height: 768 },
    { width: 1440, height: 900 },
  ];
  
  const cpuCores = [4, 6, 8, 12, 16];
  const memoryOptions = [4, 8, 16, 32];
  const downlinkOptions = [1.5, 2.5, 5.0, 7.1, 10.0];
  const rttOptions = [50, 100, 150, 200];
  
  const randomResolution = screenResolutions[Math.floor(Math.random() * screenResolutions.length)];
  
  return {
    cpu_core_num: cpuCores[Math.floor(Math.random() * cpuCores.length)],
    device_memory: memoryOptions[Math.floor(Math.random() * memoryOptions.length)],
    screen_width: randomResolution.width,
    screen_height: randomResolution.height,
    downlink: downlinkOptions[Math.floor(Math.random() * downlinkOptions.length)],
    round_trip_time: rttOptions[Math.floor(Math.random() * rttOptions.length)],
  };
}
