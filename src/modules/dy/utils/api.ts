import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { CommentListResponse, ContentListResponse, UserInfoResponse } from '../types';

/**
 * 抖音API客户端
 */
export class APIClient {
  private client: AxiosInstance;
  private baseUrl = 'https://www.douyin.com';

  /**
   * 创建API客户端
   * @param proxy 代理地址 (可选)
   */
  constructor(proxy?: string) {
    // 请求配置
    const config: AxiosRequestConfig = {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        Accept: 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Content-Type': 'application/json',
        Referer: 'https://www.douyin.com/',
        Origin: 'https://www.douyin.com',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
      },
      timeout: 30000, // 增加超时时间到30秒
      withCredentials: true, // 允许跨域请求携带凭证
    };

    // 如果提供了代理，添加代理配置
    if (proxy) {
      config.proxy = {
        protocol: proxy.startsWith('https') ? 'https' : 'http',
        host: proxy.split('://')[1].split(':')[0],
        port: parseInt(proxy.split(':').pop() || '80'),
      };
    }

    // 初始化HTTP客户端
    this.client = axios.create(config);
  }

  /**
   * 发送HTTP请求
   * @param method 请求方法
   * @param url 请求URL
   * @param options 请求选项
   * @param retries 重试次数，默认为2
   * @returns 响应数据
   */
  async request(method: string, url: string, options: any = {}, retries = 2) {
    try {
      // 添加随机延迟（100-500ms）以模拟真实用户行为
      if (options.addDelay !== false) {
        await new Promise((resolve) =>
          setTimeout(resolve, 100 + Math.random() * 400),
        );
      }

      // 添加随机数防止缓存
      const timestamp = new Date().getTime();
      const randomString = Math.random().toString(36).substring(2, 10);
      const randomParam = `_signature=${timestamp}${randomString}`;
      const separator = url.includes('?') ? '&' : '?';
      const finalUrl = `${url}${separator}${randomParam}`;

      // 设置默认的超时时间
      if (!options.timeout) {
        options.timeout = 30000;
      }

      console.log(`发送${method}请求: ${url.split('/').pop()}`);

      const res = await this.client.request({
        method,
        url: finalUrl,
        ...options,
      });

      return res.data;
    } catch (error) {
      console.error('API请求失败:', error.message);

      // 详细记录错误信息
      if (error.response) {
        console.error(`状态码: ${error.response.status}`);
        console.error(
          `响应数据: ${JSON.stringify(error.response.data).substring(0, 200)}`,
        );
      }

      // 如果还有重试次数，则重试
      if (retries > 0) {
        console.log(`请求失败，将在1秒后重试，剩余重试次数: ${retries}`);
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return this.request(method, url, options, retries - 1);
      }

      // 检查是否是超时错误
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        throw new Error(`API请求超时: ${error.message}`);
      }

      throw new Error(`API请求失败: ${error.message}`);
    }
  }

  /**
   * 获取用户信息
   * @param secUserId 用户安全ID
   * @returns 用户信息
   */
  async getUserInfo(secUserId: string): Promise<UserInfoResponse> {
    try {
      const response = await this.request(
        'GET',
        `${this.baseUrl}/aweme/v1/web/user/profile/other/`,
        {
          params: {
            device_platform: 'webapp',
            aid: '6383',
            channel: 'channel_pc_web',
            publish_video_strategy_type: '2',
            sec_user_id: secUserId,
            personal_center_strategy: '1',
            profile_other_record_enable: '1',
            version_code: '170400',
            version_name: '17.4.0',
            cookie_enabled: true,
            platform: 'PC',
            downlink: '10',
            webid: this.getWebId(),
            msToken: this.generateMsToken(),
            verifyFp: this.getVerifyFp(),
          },
          headers: {
            authority: 'www.douyin.com',
            referer: `https://www.douyin.com/user/${secUserId}`,
            Cookie: this.getCookie(),
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
          },
          timeout: 10000,
        },
      );

      // 检查响应状态码
      if (response.status_code !== 0) {
        throw new Error(response.status_msg || '获取用户信息失败');
      }

      // 检查用户数据是否存在
      if (!response.user) {
        throw new Error('获取用户信息失败：用户数据不存在');
      }

      return response;
    } catch (error) {
      // 优化错误信息
      const errorMsg = error.response?.data?.status_msg || error.message;
      throw new Error(`获取用户信息失败: ${errorMsg}`);
    }
  }

  /**
   * 获取用户发布的视频
   * @param secUserId 用户安全ID
   * @param cursor 分页游标
   * @param count 每页数量
   * @returns 视频列表
   */
  async getUserPosts(
    secUserId: string,
    cursor: number = 0,
    count: number = 18,
  ): Promise<ContentListResponse> {
    const res = await this.request(
      'GET',
      `${this.baseUrl}/aweme/v1/web/aweme/post/`,
      {
        params: {
          sec_user_id: secUserId,
          count,
          max_cursor: cursor,
          sort_type: 1,
          aid: 1128,
          device_platform: 'webapp',
          locate_query: 'false',
          show_live_replay_strategy: '1',
          need_time_list: '1',
          time_list_query: '0',
          whale_cut_token: '',
          cut_version: '1',
          publish_video_strategy_type: '2',
          platform: 'PC',
          msToken: this.generateMsToken(),
        },
        headers: {
          authority: 'www.douyin.com',
          referer: `https://www.douyin.com/user/${secUserId}`,
          Cookie: this.getCookie(),
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
        },
      },
    );

    console.log(678888, res);
    return res;
  }

  /**
   * 获取用户喜欢的视频
   * @param secUserId 用户安全ID
   * @param cursor 分页游标
   * @param count 每页数量
   * @returns 视频列表
   */
  async getUserLikes(
    secUserId: string,
    cursor: number = 0,
    count: number = 18,
  ): Promise<ContentListResponse> {
    return this.request('GET', `${this.baseUrl}/aweme/v1/web/aweme/favorite/`, {
      params: {
        sec_user_id: secUserId,
        count,
        max_cursor: cursor,
        aid: 1128,
        device_platform: 'webapp',
        min_cursor: '0',
        whale_cut_token: '',
        cut_version: '1',
        publish_video_strategy_type: '2',
        version_code: '170400',
        version_name: '17.4.0',
        platform: 'PC',
        msToken: this.generateMsToken(),
      },
      headers: {
        authority: 'www.douyin.com',
        referer: `https://www.douyin.com/user/${secUserId}`,
        Cookie: this.getCookie(),
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
      },
    });
  }

  /**
   * 获取作品评论列表
   * @param awemeId 作品ID
   * @param cursor 分页游标
   * @param count 每页数量
   * @param itemType 评论类型，默认为0
   * @returns 评论列表
   */
  async getComments(
    awemeId: string,
    cursor: string = '0',
    count: number = 20,
    itemType: string = '0',
  ): Promise<CommentListResponse> {
    try {
      // 生成一些随机值以模拟真实浏览器行为
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);

      console.log(`获取作品[${awemeId}]的评论数据，游标: ${cursor}`);

      // 使用通用request方法发送请求
      const response = await this.request(
        'GET',
        `${this.baseUrl}/aweme/v1/web/comment/list/`,
        {
          params: {
            aweme_id: awemeId,
            cursor,
            count,
            item_type: itemType,
            device_platform: 'webapp',
            aid: '6383',
            channel: 'channel_pc_web',
            version_code: '170400',
            version_name: '17.4.0',
            cookie_enabled: 'true',
            screen_width: '2560',
            screen_height: '1440',
            browser_language: 'zh-CN',
            browser_platform: 'Win32',
            browser_name: 'Chrome',
            browser_version: '122.0.6261.95',
            browser_online: 'true',
            platform: 'PC',
            downlink: '10',
            webid: this.getWebId(),
            msToken: this.generateMsToken(),
            verifyFp: this.getVerifyFp(),
            _signature: timestamp + randomString,
          },
          headers: {
            authority: 'www.douyin.com',
            referer: `https://www.douyin.com/video/${awemeId}`,
            'sec-ch-ua':
              '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            Cookie: this.getCookie(),
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
          },
        },
      );

      // 记录响应信息
      console.log('评论API响应状态码:', response, response.status_code);
      if (response.comments) {
        console.log(`获取评论成功，共${response.comments.length}条`);
      }

      return response;
    } catch (error) {
      console.error('获取评论失败:', error.message);
      throw new Error(`获取评论列表失败: ${error.message}`);
    }
  }

  // 添加辅助方法
  private getWebId(): string {
    return '7363303652171253302'; // 建议动态生成
  }

  private getVerifyFp(): string {
    return 'verify_mb80zbn2_c8112660_f6ed_8db3_5177_a0bc62654bbe'; // 建议动态生成
  }

  // 添加Cookie管理方法
  private getCookie(): string {
    // 这里返回有效的Cookie字符串
    // 建议从配置或环境变量中读取
    return 'store-region=cn-js; store-region-src=uid; UIFID_TEMP=e438e504399eecf9c2f65594851517a53fcd0a47c3feace6f386418d12bbc04d11d7f2459d2cf24e4c44810b3cb45b7d11971c2d48d29964a50f91620510a4194db3252f25fd114fa61f41857049ef40; fpk1=U2FsdGVkX1/EqrUqPe9GuweNkNe5qbhp/mEh9UTB9vRHkOqjp05tjNC+bFswqUUAysyR0CACuh7YyIt3DVpFZw==; fpk2=90e0551f0a34ace8d44b6df2ec6bc7a9; is_staff_user=false; bd_ticket_guard_client_web_domain=2; UIFID=e438e504399eecf9c2f65594851517a53fcd0a47c3feace6f386418d12bbc04d11d7f2459d2cf24e4c44810b3cb45b7d37cee5402c3f30a466db176064c8296e71d291e0f36dc7641ad8952b34ba16c289143a91149258016e0b3fbc49c17a8d1ae1968058d727e5140f0ded112830fdc5406772ed654705722054bd80e8f2f734bd8e4b31185ddbb1f17100741ec839fdf08940dece5a829794fb819ad98bbc; hevc_supported=true; live_use_vvc=%22false%22; xgplayer_device_id=91533588648; __live_version__=%221.1.2.5981%22; xgplayer_user_id=76397338355; my_rd=2; d_ticket=473ce27e9d35b92691fa716b28ac782da278d; SEARCH_RESULT_LIST_TYPE=%22single%22; theme=%22light%22; __security_mc_1_s_sdk_crypt_sdk=0a7662ed-49b0-a485; __security_mc_1_s_sdk_cert_key=1bed030a-463e-bc6c; passport_csrf_token=a727d5ab1c4c5d3a2dfe36a34c4f20d6; passport_csrf_token_default=a727d5ab1c4c5d3a2dfe36a34c4f20d6; dy_swidth=2560; dy_sheight=1440; is_dash_user=1; s_v_web_id=verify_mb80zbn2_c8112660_f6ed_8db3_5177_a0bc62654bbe; SelfTabRedDotControl=%5B%5D; publish_badge_show_info=%220%2C0%2C0%2C1749359122488%22; enter_pc_once=1; volume_info=%7B%22isUserMute%22%3Afalse%2C%22isMute%22%3Afalse%2C%22volume%22%3A0.992%7D; __druidClientInfo=JTdCJTIyY2xpZW50V2lkdGglMjIlM0EyOTglMkMlMjJjbGllbnRIZWlnaHQlMjIlM0E2OTQlMkMlMjJ3aWR0aCUyMiUzQTI5OCUyQyUyMmhlaWdodCUyMiUzQTY5NCUyQyUyMmRldmljZVBpeGVsUmF0aW8lMjIlM0ExJTJDJTIydXNlckFnZW50JTIyJTNBJTIyTW96aWxsYSUyRjUuMCUyMChXaW5kb3dzJTIwTlQlMjAxMC4wJTNCJTIwV2luNjQlM0IlMjB4NjQpJTIwQXBwbGVXZWJLaXQlMkY1MzcuMzYlMjAoS0hUTUwlMkMlMjBsaWtlJTIwR2Vja28pJTIwQ2hyb21lJTJGMTIyLjAuNjI2MS45NSUyMFNhZmFyaSUyRjUzNy4zNiUyMiU3RA==; WallpaperGuide=%7B%22showTime%22%3A0%2C%22closeTime%22%3A0%2C%22showCount%22%3A0%2C%22cursor1%22%3A10%2C%22cursor2%22%3A2%7D; FOLLOW_NUMBER_YELLOW_POINT_INFO=%22MS4wLjABAAAAb1OBhd7YrveXjQ0RKQ8DIb2iNTOhzJyWvhmlzn8KFH33Ng1YgLcElEK_7DksSxXi%2F1749657600000%2F0%2F1749623695589%2F0%22; FOLLOW_LIVE_POINT_INFO=%22MS4wLjABAAAAb1OBhd7YrveXjQ0RKQ8DIb2iNTOhzJyWvhmlzn8KFH33Ng1YgLcElEK_7DksSxXi%2F1749830400000%2F0%2F1749824064600%2F0%22; strategyABtestKey=%************.852%22; passport_assist_user=CkEc18E2gFbG1CQaw_um0TPegOG8Mr9wPem3o0LKDAis_X0FYMvRbFKVfIeY029S2mtKX3Q5PwlmmmG9lEArMFE7FBpKCjwAAAAAAAAAAAAATx2rn8oQ1NeYXhE2HpANJ-eysO9ep425WlwWgknnQRTbaCvgeUrvaKDcrelv7cgBFikQz4f0DRiJr9ZUIAEiAQOmonHv; n_mh=dLxh4cdh7oaWtkiqyGVrB2FXHHLs4CGlLsuUMoRuCnc; sid_guard=416acd544eafa1482eb3a2785c0a4ed8%7C1749884058%7C5184000%7CWed%2C+13-Aug-2025+06%3A54%3A18+GMT; uid_tt=cad091eee8fcfa753530d5761e615785; uid_tt_ss=cad091eee8fcfa753530d5761e615785; sid_tt=416acd544eafa1482eb3a2785c0a4ed8; sessionid=416acd544eafa1482eb3a2785c0a4ed8; sessionid_ss=416acd544eafa1482eb3a2785c0a4ed8; sid_ucp_v1=1.0.0-KGQ0ZWI5NDJjZTEwMjU2NWZiMjNmMGVjYTdkNzlkYjY1MGY3ZTdjMmYKIQiaioCUwczPBxCaubTCBhjvMSAMMInShLoGOAVA-wdIBBoCaGwiIDQxNmFjZDU0NGVhZmExNDgyZWIzYTI3ODVjMGE0ZWQ4; ssid_ucp_v1=1.0.0-KGQ0ZWI5NDJjZTEwMjU2NWZiMjNmMGVjYTdkNzlkYjY1MGY3ZTdjMmYKIQiaioCUwczPBxCaubTCBhjvMSAMMInShLoGOAVA-wdIBBoCaGwiIDQxNmFjZDU0NGVhZmExNDgyZWIzYTI3ODVjMGE0ZWQ4; login_time=1749884059252; __security_mc_1_s_sdk_sign_data_key_web_protect=2de64a65-4007-8952; _bd_ticket_crypt_cookie=51a9e647e27432d2533d586265e8d1ef; DiscoverFeedExposedAd=%7B%7D; ttwid=1%7CcSUEKfDiAaUpKyow_OqIJMvR6-QE1piKFRpk_BeUYL8%7C1749884062%7C056d5ee4a6e9940545d5f6c9d136d17b1e410d309c5baba79719d227f37cb956; __ac_signature=_02B4Z6wo00f01zp535AAAIDBtFH1O3BSWoM6WdsAAKbdc7SNIyEcU0td1aZdDvenptJ8tG2pT21r79xA98-otSj5wfO1eoJWMgIe5GWpiQgF3kv24iYH4zFXrmJPlaAoaPfiLAQ0.oSLIfN5fc; douyin.com; xg_device_score=7.815481389136538; device_web_cpu_core=12; device_web_memory_size=8; architecture=amd64; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCUEd1SlhyS214dGR2L29KVjl3NGFhdlE1Y0wwUHNpbXA4VmE5b2lRU2JEVk1aZ3BydFgxa2VHL00xNnlPUm5EVVV2WXlBd2hRTDBnTWxTa1F3SDlEY3c9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D; download_guide=%223%2F20250614%2F1%22; odin_tt=be8132831497808294001f5897deecf1cdd253a958710fdcba2dad6f4f66aee23ed80c271ecf6192a65f67b0f527f380a308fd15e784488c2377186dfd1c36c3; __ac_nonce=0684d59c000847c25e2ef; passport_fe_beating_status=false; IsDouyinActive=true; home_can_add_dy_2_desktop=%220%22; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A2560%2C%5C%22screen_height%5C%22%3A1440%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A12%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A100%7D%22';
  }

  /**
   * 生成msToken
   * 这是抖音API请求需要的一个参数
   */
  private generateMsToken(): string {
    const chars =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    const length = 107; // 通常msToken长度为107

    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return result;
  }

  // 添加生成票据保护数据的方法
  private getTicketGuardData(): string {
    // 实际项目中应该动态生成，这里使用固定值作为示例
    return 'eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCUEd1SlhyS214dGR2L29KVjl3NGFhdlE1Y0wwUHNpbXA4VmE5b2lRU2JEVk1aZ3BydFgxa2VHL00xNnlPUm5EVVV2WXlBd2hRTDBnTWxTa1F3SDlEY3c9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ==';
  }
}
