import axios from 'axios';

/**
 * 从 URL 中提取 Sec_id
 * @param userUrl
 * @returns
 */
export const getTiktokSecId = (userUrl: string) => {
  const reg = /(?<=user\/)[^?]+/g;
  const result = userUrl.match(reg);
  if (result) return result[0];
  return null;
};

/**
 * 获取用户Sec_Id
 * @param userUrl 用户URL（短链或长链）
 * @returns 用户Sec_Id
 */
export const getUserSecId = async (userUrl: string): Promise<string> => {
  let userSecId: string | null = '';
  const urlRegex = /www\.douyin\.com\/user\//;

  if (urlRegex.test(userUrl)) {
    // 长链接直接使用
    userSecId = userUrl;
  } else {
    try {
      // 短链接需要获取重定向后的URL
      const response = await axios.get(userUrl, {
        maxRedirects: 5,
        validateStatus: function (status) {
          return status >= 200 && status < 400; // 接受重定向状态码
        },
      });

      userSecId = response.request.res.responseUrl; // 获取重定向URL
    } catch (error) {
      throw new Error(`无法获取重定向URL: ${error.message}`);
    }
  }

  // 从URL中提取Sec_Id
  userSecId = getTiktokSecId(userSecId);

  if (!userSecId) throw new Error('Sec_Id 获取失败');
  return userSecId;
};

/**
 * 检查日期格式并转换
 * @param dateStr 日期字符串（YYYY-MM-DD）或天数
 * @param defaultDate 默认日期
 * @param label 标签（用于错误提示）
 * @returns Date对象
 */
export const checkDate = (
  dateStr: string | number,
  defaultDate: Date,
  label: string,
): Date => {
  // 如果是数字，表示天数
  if (!isNaN(Number(dateStr))) {
    const days = Number(dateStr);
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date;
  }

  // 否则尝试解析日期字符串
  const date = new Date(dateStr);
  if (isNaN(date.getTime())) {
    console.warn(`无效的${label}格式: ${dateStr}，使用默认值`);
    return defaultDate;
  }
  return date;
}; 