/**
 * 抖音API参数生成器
 * 负责生成各种动态参数，如msToken、verifyFp、a_bogus等
 */

import { createHash, randomBytes } from 'crypto';

export class DouyinParamGenerator {
  private webId: string;
  private uifid: string;

  constructor(webId?: string, uifid?: string) {
    this.webId = webId || this.generateWebId();
    this.uifid = uifid || this.generateUifid();
  }

  /**
   * 生成WebID
   */
  private generateWebId(): string {
    // 生成19位数字ID
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
    return timestamp + random;
  }

  /**
   * 生成UIFID
   */
  private generateUifid(): string {
    // 生成128位十六进制字符串
    return randomBytes(64).toString('hex');
  }

  /**
   * 获取WebID
   */
  getWebId(): string {
    return this.webId;
  }

  /**
   * 获取UIFID
   */
  getUifid(): string {
    return this.uifid;
  }

  /**
   * 从Cookie中提取UIFID
   */
  extractUifidFromCookie(cookie: string): string {
    const uifidMatch = cookie.match(/UIFID=([^;]+)/);
    if (uifidMatch) {
      this.uifid = uifidMatch[1];
      return this.uifid;
    }
    return this.uifid;
  }

  /**
   * 生成verifyFp参数
   */
  generateVerifyFp(): string {
    const generateRandomString = (length: number) => {
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    };
    
    return `verify_${generateRandomString(8)}_${generateRandomString(8)}_${generateRandomString(4)}_${generateRandomString(4)}_${generateRandomString(4)}_${generateRandomString(12)}`;
  }

  /**
   * 生成msToken参数
   */
  generateMsToken(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
    let result = '';
    const length = 107;

    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    // 添加URL编码的等号
    result += '%3D%3D';
    return result;
  }

  /**
   * 生成a_bogus参数
   * 注意：这是一个简化版本，真实的a_bogus算法非常复杂
   * 建议使用专门的a_bogus生成服务或库
   */
  generateABogus(url?: string, userAgent?: string): string {
    try {
      // 这是一个模拟的a_bogus生成算法
      // 实际的算法需要逆向工程抖音的JavaScript代码
      
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 15);
      const webId = this.getWebId();
      
      // 创建基础字符串
      const baseString = `${timestamp}${random}${webId}${url || ''}${userAgent || ''}`;
      
      // 使用MD5哈希
      const hash = createHash('md5').update(baseString).digest('hex');
      
      // 生成类似真实a_bogus的格式
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
      let result = '';
      
      // 使用哈希值作为种子生成随机字符
      for (let i = 0; i < 64; i++) {
        const index = parseInt(hash.charAt(i % hash.length), 16) % chars.length;
        result += chars.charAt(index);
      }
      
      return encodeURIComponent(result);
    } catch (error) {
      console.warn('a_bogus生成失败，使用备用方案:', error.message);
      
      // 备用方案：生成随机字符串
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
      let result = '';
      for (let i = 0; i < 64; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return encodeURIComponent(result);
    }
  }

  /**
   * 生成X-Bogus参数（如果需要）
   */
  generateXBogus(): string {
    // 类似a_bogus的生成逻辑
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 15);
    const baseString = `${timestamp}${random}${this.webId}`;
    
    const hash = createHash('sha256').update(baseString).digest('hex');
    return hash.substring(0, 32);
  }

  /**
   * 生成完整的请求参数
   */
  generateRequestParams(baseParams: Record<string, any>): Record<string, any> {
    return {
      ...baseParams,
      webid: this.getWebId(),
      uifid: this.getUifid(),
      msToken: this.generateMsToken(),
      verifyFp: this.generateVerifyFp(),
      a_bogus: this.generateABogus(),
    };
  }

  /**
   * 重置所有动态参数
   */
  reset(): void {
    this.webId = this.generateWebId();
    this.uifid = this.generateUifid();
  }
}

/**
 * 全局参数生成器实例
 */
export const globalParamGenerator = new DouyinParamGenerator();
