/**
 * 将各种格式的时间字符串或数字转换为时间戳
 * @param time 时间字符串/时间戳
 * @returns 时间戳(秒)
 */
export function convertToTimestamp(time: string | number): number {
  if (typeof time === 'number') {
    // 如果已经是数字，直接返回
    return time;
  }
  
  try {
    // 尝试解析为日期格式 (YYYY-MM-DD)
    const date = new Date(time);
    if (!isNaN(date.getTime())) {
      return Math.floor(date.getTime() / 1000);
    }
  } catch (error) {
    console.error('日期解析错误:', error);
  }
  
  return 0;
}

/**
 * 按时间范围过滤内容列表
 * @param items 内容列表
 * @param earliest 最早时间
 * @param latest 最晚时间
 * @returns 过滤后的列表
 */
export function filterByTimeRange<T extends { create_time: number }>(
  items: T[],
  earliest?: string | number,
  latest?: string | number
): T[] {
  if (!items || items.length === 0) {
    return [];
  }
  
  let filteredItems = [...items];
  
  // 按最早时间过滤
  if (earliest) {
    const earliestTimestamp = convertToTimestamp(earliest);
    if (earliestTimestamp > 0) {
      filteredItems = filteredItems.filter(item => item.create_time >= earliestTimestamp);
    }
  }
  
  // 按最晚时间过滤
  if (latest) {
    const latestTimestamp = convertToTimestamp(latest);
    if (latestTimestamp > 0) {
      filteredItems = filteredItems.filter(item => item.create_time <= latestTimestamp);
    }
  }
  
  return filteredItems;
}

/**
 * 检查日期参数并返回Date对象
 * @param dateValue 日期值(字符串或数字)
 * @param defaultDate 默认日期
 * @param tipName 日期名称(用于日志)
 * @returns Date对象
 */
export function checkDate(
  dateValue: string | number | undefined,
  defaultDate: Date,
  tipName: string = '日期'
): Date {
  if (!dateValue) {
    return defaultDate;
  }
  
  let resultDate = defaultDate;
  
  // 如果是数字，表示过去的天数
  if (typeof dateValue === 'number') {
    const today = new Date();
    resultDate = new Date(today.setDate(today.getDate() - dateValue));
  } 
  // 如果是字符串，尝试解析
  else if (typeof dateValue === 'string') {
    // 支持多种日期格式
    const formats = ['YYYY-MM-DD', 'YYYY/MM/DD', 'MM-DD-YYYY', 'MM/DD/YYYY'];
    const parsedDate = parseDate(dateValue);
    
    if (parsedDate && !isNaN(parsedDate.getTime())) {
      resultDate = parsedDate;
    } else {
      console.warn(`无效的${tipName}: ${dateValue}`);
    }
  }
  
  console.log(`${tipName}: ${resultDate.toISOString().split('T')[0]}`);
  return resultDate;
}

/**
 * 尝试解析各种格式的日期字符串
 * @param dateStr 日期字符串
 * @returns Date对象
 */
function parseDate(dateStr: string): Date | null {
  // 尝试直接解析
  const date = new Date(dateStr);
  if (!isNaN(date.getTime())) {
    return date;
  }
  
  // 尝试解析 YYYY/MM/DD 或 YYYY-MM-DD
  const patterns = [
    /^(\d{4})[-/](\d{1,2})[-/](\d{1,2})$/,  // YYYY-MM-DD or YYYY/MM/DD
    /^(\d{1,2})[-/](\d{1,2})[-/](\d{4})$/,  // MM-DD-YYYY or MM/DD/YYYY
  ];
  
  for (const pattern of patterns) {
    const match = dateStr.match(pattern);
    if (match) {
      const [_, part1, part2, part3] = match;
      if (pattern === patterns[0]) {
        // YYYY-MM-DD
        return new Date(parseInt(part1), parseInt(part2) - 1, parseInt(part3));
      } else {
        // MM-DD-YYYY
        return new Date(parseInt(part3), parseInt(part1) - 1, parseInt(part2));
      }
    }
  }
  
  return null;
} 