export interface UserInfoDto {
  uid: string;
  sec_uid: string;
  nickname: string;
  signature: string;
  avatar_url: string;
  following_count: number;
  follower_count: number;
  total_favorited: number;
  aweme_count: number;
  // 新增可选字段
  ip_location?: string;
  province?: string;
  city?: string;
  short_id?: string;
}

/**
 * 抖音API接口响应基础类型
 */
export interface BaseResponse {
  status_code: number;
  status_msg?: string;
}

/**
 * 抖音用户信息原始类型
 */
export interface UserInfo {
  uid: string;
  sec_uid: string;
  nickname: string;
  signature?: string;
  avatar_larger?: {
    url_list: string[];
  };
  avatar_medium?: {
    url_list: string[];
  };
  unique_id: string;
  following_count: number;
  follower_count: number;
  total_favorited: number;
  aweme_count: number;
  ip_location?: string;
  province?: string;
  city?: string;
  short_id?: string;
}

/**
 * 用户信息响应类型
 */
export interface UserInfoResponse extends BaseResponse {
  user: UserInfo;
}

/**
 * 抖音内容统计类型
 */
export interface ContentStatistics {
  /** 点赞数 */
  digg_count: number;
  /** 评论数 */
  comment_count: number;
  /** 收藏数 */
  collect_count: number;
  /** 分享数 */
  share_count: number;
  /** 播放数（可选） */
  play_count?: number;
}
/**
 * 抖音内容作者类型
 */
export interface ContentAuthor {
  uid: string;
  sec_uid: string;
  nickname: string;
  unique_id?: string;
  avatar_thumb?: {
    url_list: string[];
  };
}


/**
 * 视频类型
 */
export interface VideoInfo {
  play_addr: any;
  cover: {
    url_list: string[];
  };
  dynamic_cover?: {
    url_list: string[];
  };
  duration: number;
  ratio: string;
}

/**
 * 图片类型
 */
export interface ImageInfo {
  url_list: string[];
  uri: string;
  width: number;
  height: number;
}

/**
 * 音乐类型
 */
export interface MusicInfo {
  id: string;
  title: string;
  author: string;
  cover_large?: {
    url_list: string[];
  };
  play_url?: {
    url_list: string[];
  };
  duration: number;
}

/**
 * 抖音内容项类型
 */
export interface ContentItem {
  aweme_id: string;
  item_title: string;
  desc: string;
  create_time: number;
  duration: number;
  aweme_type: number; // 普通视频:0, 图文:2, 音乐:22
  author: ContentAuthor;
  music: MusicInfo;
  video?: VideoInfo;
  images?: ImageInfo[];
  statistics: ContentStatistics;
  text_extra?: {
    hashtag_name: string;
    user_id: string;
  }[];
  text_extra_position?: any[];
}

/**
 * 抖音内容列表响应
 */
export interface ContentListResponse extends BaseResponse {
  aweme_list: ContentItem[];
  has_more: boolean;
  max_cursor: number;
  min_cursor: number;
}

/**
 * 评论用户信息
 */
export interface CommentUser {
  uid: string;
  sec_uid: string;
  nickname: string;
  avatar_thumb?: {
    url_list: string[];
  };
  ip_location?: string;
}

/**
 * 评论详情
 */
export interface CommentInfo {
  cid: string;
  text: string;
  create_time: number;
  digg_count: number;
  reply_comment_total: number;
  user: CommentUser;
  is_top?: boolean;
  reply_comment?: CommentInfo[];
}

/**
 * 评论列表响应
 */
export interface CommentListResponse extends BaseResponse {
  comments: CommentInfo[];
  cursor: string;
  has_more: boolean;
  total: number;
}
