import { Body, Controller, Post, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DyService } from '../services/dy.service';
import { AccountReqDto } from '../dto/req/account.req.dto';
import { AccountResDto } from '../dto/res/account.res.dto';
import { AudioResDto } from '../dto/res/audio.res.dto';
import { AudioReqDto } from '../dto/req/audio.req.dto';
import { CommentReqDto } from '../dto/req/comment.req.dto';
import { CommentResDto } from '../dto/res/comment.res.dto';

@ApiTags('抖音账号')
@Controller('dy')
export class DyController {
  constructor(private readonly dyService: DyService) {}

  @ApiOperation({ summary: '获取抖音账号信息和作品列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功',
    type: AccountResDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  @Post('account')
  async getAccountData(
    @Body() accountDto: AccountReqDto,
  ): Promise<AccountResDto> {
    return this.dyService.getAccountData(accountDto);
  }

  @ApiOperation({ summary: '获取抖音用户发布的作品列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功',
    type: AccountResDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  @Post('posts')
  async getUserPosts(
    @Body() accountDto: AccountReqDto,
  ): Promise<AccountResDto> {
    // 设置为发布内容类型
    accountDto.tab = 'post';
    return this.dyService.getAccountData(accountDto);
  }

  @ApiOperation({ summary: '获取抖音用户喜欢的作品列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功',
    type: AccountResDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  @Post('likes')
  async getUserLikes(
    @Body() accountDto: AccountReqDto,
  ): Promise<AccountResDto> {
    // 设置为喜欢内容类型
    accountDto.tab = 'like';
    return this.dyService.getAccountData(accountDto);
  }

  @Post('audio2text')
  @ApiOperation({ summary: '音频转文字' })
  @ApiResponse({ status: 200, type: AudioResDto })
  async audioToText(@Body() audioDto: AudioReqDto): Promise<AudioResDto> {
    return await this.dyService.audioToText(audioDto);
  }

  @Post('comments')
  @ApiOperation({ summary: '获取作品评论列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功',
    type: CommentResDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  async getComments(@Body() commentDto: CommentReqDto): Promise<CommentResDto> {
    return this.dyService.getComments(commentDto);
  }
}
