# API接口文档使用指南

本API文档使用Swagger UI和Knife4j提供了两种视图方式，可以方便地查看和测试所有API。

## 文档入口

- **完整API文档**：访问 `/api-docs` 查看所有API
- **社交媒体API**：访问 `/api-docs/social-media` 查看抖音相关API
- **用户认证API**：访问 `/api-docs/auth` 查看用户与认证相关API
- **Knife4j视图**：访问 `/doc.html` 查看更美观的Knife4j视图

## 主要模块

### 抖音账号模块 (DY)

提供抖音账号信息获取功能：

- **获取抖音账号信息**：`POST /dy/account`
  - 根据sec_user_id获取用户信息和内容列表
  - 支持按时间范围过滤和分页

### 抖音模块 (Douyin)

提供抖音视频获取功能：

- **获取UP主信息**：`POST /douyin/getUpInfo`
  - 根据用户链接或ID获取用户发布或喜欢的视频列表

## 认证说明

部分API需要JWT认证，请在调用API前通过登录接口获取token，并在请求头中添加：

```
Authorization: Bearer YOUR_TOKEN_HERE
```

在Swagger UI中，可以点击右上角的"Authorize"按钮，填入token进行认证。

## 类型说明

- **sec_user_id**: 抖音用户唯一标识，可从用户主页URL中获取
- **tab**: 内容类型，支持"post"(发布)和"like"(喜欢)
- **cursor**: 分页游标，用于获取下一页数据

## 请求示例

获取抖音账号信息：

```json
{
  "sec_user_id": "MS4wLjABAAAAkJai4HM",
  "tab": "post",
  "earliest": "2023-01-01",
  "latest": "2023-12-31",
  "pages": 5,
  "count": 20
}
```

## 响应示例

成功响应：

```json
{
  "user_info": {
    "uid": "123456789",
    "sec_uid": "MS4wLjABAAAAkJai4HM",
    "nickname": "用户昵称",
    "signature": "用户签名",
    "avatar_url": "https://example.com/avatar.jpg",
    "following_count": 100,
    "follower_count": 1000,
    "total_favorited": 5000,
    "aweme_count": 200
  },
  "items": [
    {
      "id": "7123456789",
      "desc": "视频描述",
      "create_time": 1672502400,
      "video_url": "https://example.com/video.mp4",
      "author": {
        "uid": "123456789",
        "sec_uid": "MS4wLjABAAAAkJai4HM",
        "nickname": "用户昵称"
      },
      "statistics": {
        "digg_count": 1000,
        "comment_count": 100,
        "collect_count": 50,
        "share_count": 30
      },
      "type": "video"
    }
  ],
  "cursor": "1675180800",
  "has_more": true,
  "status_code": 0,
  "status_msg": ""
}
```

## 错误处理

API返回的错误格式统一为：

```json
{
  "status_code": 400,
  "status_msg": "错误信息",
  "user_info": null,
  "items": [],
  "cursor": "0",
  "has_more": false
}
```

常见错误码：
- 400: 参数错误
- 401: 未认证
- 403: 无权限
- 500: 服务器错误 