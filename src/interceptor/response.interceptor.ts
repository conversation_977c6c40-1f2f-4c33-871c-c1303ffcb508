import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable, catchError, map, throwError } from 'rxjs';
import { Response } from 'express';

@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  intercept(
    context: ExecutionContext,
    next: CallHandler<any>,
  ): Observable<any> | Promise<Observable<any>> {
    const ctx = context.switchToHttp();

    return next.handle().pipe(
      map((data) => {
        // 检查数据是否已经是标准响应格式（有code和data字段）
        // 这样可以避免重复包装已经格式化过的响应
        if (data && typeof data === 'object' && 'code' in data && 'data' in data) {
          return data;
        }
        
        // 标准响应格式包装
        return {
          code: 200,
          data: data,
          msg: '成功',
        };
      }),
      catchError((err) => {
        let errorMsg = err?.response?.message || err?.message;
        if (Array.isArray(errorMsg)) {
          errorMsg = errorMsg.join(',');
        }
        return throwError({
          code: -1,
          msg: errorMsg,
        });
      }),
    );
  }
}
