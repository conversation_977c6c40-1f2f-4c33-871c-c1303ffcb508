/**
 * 统一的API类型定义
 */

// 基础响应类型
export interface BaseResponse<T = any> {
  success: boolean;
  code: string | number;
  message: string;
  data: T;
  timestamp: string;
}

// 分页请求参数
export interface PaginationParams {
  pageIndex?: number;
  pageSize?: number;
  sort?: SortParams;
}

// 排序参数
export interface SortParams {
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC' | 'asc' | 'desc';
}

// 分页响应数据
export interface PaginatedData<T> {
  data: T[];
  total: number;
  pageIndex: number;
  pageSize: number;
  totalPages: number;
}

// 用户相关类型
export interface UserInfo {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
  role: string;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: UserInfo;
  token: string;
  refreshToken?: string;
  expiresIn: number;
}

// 商户相关类型
export interface MerchantEntity {
  id: string;
  name: string;
  projectName: string;
  knowledge: string;
  accountPositioning?: string;
  selectedBenchmarkingAccountId?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface CreateMerchantRequest {
  name: string;
  projectName: string;
  knowledge: string;
  accountPositioning?: string;
  selectedBenchmarkingAccountId?: string;
}

export interface UpdateMerchantRequest extends Partial<CreateMerchantRequest> {}

// IP账号相关类型
export interface IpAccountEntity {
  id: string;
  benchmarkingAccount: string;
  benchmarkingUrl: string;
  benchmarkingAnalysis: string;
  uid?: string;
  secUid?: string;
  nickname?: string;
  signature?: string;
  avatarUrl?: string;
  followingCount: number;
  followerCount: number;
  totalFavorited: number;
  awemeCount: number;
  ipLocation?: string;
  province?: string;
  city?: string;
  shortId?: string;
  statusCode: number;
  statusMsg: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateIpAccountRequest {
  benchmarkingAccount: string;
  benchmarkingUrl: string;
  benchmarkingAnalysis: string;
}

export interface IpAccountQueryParams extends PaginationParams {
  benchmarkingAccount?: string;
  status?: string;
  province?: string;
  city?: string;
}



// 工作流相关类型
export interface WorkflowExecutionRequest {
  workflowId: string;
  parameters: Record<string, any>;
  description?: string;
}

export interface WorkflowExecutionResponse {
  executionId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: any;
  error?: string;
  startTime: string;
  endTime?: string;
  duration?: number;
}

// 视频相关类型
export interface VideoEntity {
  id: string;
  title: string;
  description?: string;
  segment: string; // JSON字符串
  merchantId: string;
  humanId: number;
  videoUrl?: string;
  status: 'draft' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
}

export interface VideoGenerationRequest {
  merchantId: string;
  humanId: number;
  segment: any[];
}

// 对标分析相关类型
export interface BenchmarkingEntity {
  id: string;
  awemeId: string;
  description: string;
  videoUrl: string;
  audioUrl: string;
  audioText: string;
  upName: string;
  type: string;
  ipId: string;
  diggCount: number;
  commentCount: number;
  collectCount: number;
  shareCount: number;
  createTime?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BenchmarkingQueryParams extends PaginationParams {
  ipId?: string;
  upName?: string;
  type?: string;
  startDate?: string;
  endDate?: string;
}

// 错误响应类型
export interface ErrorResponse {
  success: false;
  code: string;
  message: string;
  data: null;
  details?: any;
  timestamp: string;
  path: string;
  method: string;
}

// 通用查询参数
export interface CommonQueryParams extends PaginationParams {
  keyword?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
}

// 文件上传相关
export interface FileUploadResponse {
  url: string;
  filename: string;
  size: number;
  mimeType: string;
  uploadedAt: string;
}

// 统计数据类型
export interface StatsData {
  period: 'day' | 'week' | 'month' | 'year';
  data: Array<{
    date: string;
    value: number;
    label?: string;
  }>;
  total: number;
  growth?: number;
}

// API端点类型定义
export interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  description: string;
  requestType?: any;
  responseType?: any;
  requiresAuth?: boolean;
  permissions?: string[];
}

// WebSocket消息类型
export interface WebSocketMessage<T = any> {
  type: string;
  payload: T;
  timestamp: string;
  id?: string;
}

// 通知相关类型
export interface NotificationEntity {
  id: string;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'error' | 'success';
  read: boolean;
  userId: string;
  createdAt: string;
}

// 系统配置类型
export interface SystemConfig {
  maintenance: boolean;
  features: {
    [featureName: string]: boolean;
  };
  limits: {
    maxFileSize: number;
    maxRequestsPerMinute: number;
    maxProjectsPerUser: number;
  };
}