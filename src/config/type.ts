import { TypeOrmModuleOptions } from '@nestjs/typeorm';

export type IConfig = {
  // 项目URL的前缀
  prefix?: string;

  // 数据库配置
  database?: {
    type: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    entities: string[];
    synchronize: boolean;
  };

  // JWT 配置
  jwt?: {
    // JWT 密钥
    secret: string;
    // JWT 过期时间
    expiresIn: string;
  };

  // 密码相关的密钥配置
  secret?: {
    // 密码加密密钥
    passwordSecret?: string;
    // 用户名
    username?: string;
    // 密码
    password?: string;
  };

  // Redis 配置
  redis?: {
    type: string;
    host: string;
    port: number;
    password?: string;
  };

  // 密码配置
  password?: {
    // 密码加密密钥
    secret: string;
  };

  // 邮件服务配置
  mail?: {
    // 邮件服务器主机地址
    host: string;
    // 邮件服务器端口号
    port: number;
    // 是否使用安全连接
    secure: boolean;
    // 邮件服务器认证信息
    auth: {
      // 邮件服务器用户名
      user: string;
      // 邮件服务器授权码
      pass: string;
      // 邮件服务器认证密钥
      secret?: string;
    };
  };
  coze?: {
    token: string;
    baseURL: string;
  };
  douyin?: {
    USE_MOCK_COMMENTS?: string;
    FALLBACK_TO_MOCK_COMMENTS?: string;
    PROXY_URL?: string;
  };
};
