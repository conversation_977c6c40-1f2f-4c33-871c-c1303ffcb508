import { join } from 'path';
import { IConfig } from './type';

// const host = '*************';
const host = '127.0.0.1';

const config: IConfig = {
  // 数据库配置
  database: {
    type: 'mysql',
    host: host,
    port: 3306,
    username: 'root',
    password: 'zengtao123',
    database: 'qf',
    entities: [
      join(__dirname, '..', 'modules', '**', '**', '*.entity.{ts,js}'),
    ],
    synchronize: true, // 在开发环境下可以使用，生产环境需要手动管理数据库结构变化
    // namingStrategy: new SnakeNamingStrategy(),
  },
  redis: {
    type: 'redis',
    host: host,
    port: 6379,
    // password: 'zengtao123',
  },
  coze: {
    token:
      'pat_1ndHHtksAtROa9JyQF9TLx4eezahHg4PM7uDl0Smy0Zy1h1ch6PHcK6Q1fJ0CBs0',
    baseURL: 'https://api.coze.cn',
  },
  // 抖音API配置
  douyin: {
    // 模拟数据已禁用
    USE_MOCK_COMMENTS: 'false',
    FALLBACK_TO_MOCK_COMMENTS: 'false',
    // 代理设置（如需使用）
    PROXY_URL: '',
  },
};

export default config;
