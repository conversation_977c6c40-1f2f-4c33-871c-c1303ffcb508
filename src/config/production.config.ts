import { join } from 'path';
import { IConfig } from './type';

const host = '*************';

// *************:12700
const config: IConfig = {
  // 数据库配置
  database: {
    type: 'mysql',
    // host: "pc-bp1w4u30y344jc1b7.rwlb.rds.aliyuncs.com",
    host: host,
    port: 3559,
    username: 'root',
    password: 'zengtao123',
    database: 'qf',
    entities: [join(process.cwd(), '**', '*.entity.js')],
    synchronize: false, // 在开发环境下可以使用，生产环境需要手动管理数据库结构变化
    // namingStrategy: new SnakeNamingStrategy(),
  },
  redis: {
    type: 'redis',
    host: host,
    port: 3557,
    password: 'zengtao123',
  },
  coze: {
    token:
      'pat_1ndHHtksAtROa9JyQF9TLx4eezahHg4PM7uDl0Smy0Zy1h1ch6PHcK6Q1fJ0CBs0',
    baseURL: 'https://api.coze.cn',
  },
};

export default config;
