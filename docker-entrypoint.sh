#!/bin/sh

# 设置工作目录
cd /usr/src/app

# 安装基础工具
apt-get update


# 安装git工具
if ! command -v git &> /dev/null; then
    echo "git 未安装，正在安装..."
    apt-get update && apt-get install -y git
else
    echo "git 已安装"
fi

# 安装ffmpeg
if ! command -v ffmpeg &> /dev/null; then
    echo "ffmpeg 未安装，正在安装..."
    apt-get install -y ffmpeg
else
    echo "ffmpeg 已安装"
fi

# 安装python和pip
if ! command -v python3 &> /dev/null; then
    echo "python3 未安装，正在安装..."
    apt-get install -y python3 python3-pip
else
    echo "python3 已安装"
fi

# 安装whisper
echo "安装/更新 whisper..."
pip3 install -U openai-whisper

# 显示当前目录和文件列表
echo "第一次当前目录:"
pwd
echo "目录内容:"
ls -la

# 更新代码
if [ -d ".git" ]; then
    echo "Updating code from git..."
    git pull origin master
else
    echo "克隆代码仓库..."
    git clone https://gitee.com/zengtao123/qf_backend.git .
fi

# 只删除 dist 目录
rm -rf dist

# 安装依赖和 NestJS CLI
yarn install

# 打包项目
yarn build

# 显示当前目录和文件列表
echo "第二次当前目录:"
pwd
echo "目录内容:"
ls -la

# 验证构建结果
if [ ! -f "dist/main.js" ]; then
    echo "构建失败: dist/main.js 不存在"
    exit 1
fi

# 启动应用
echo "Starting application..."
yarn start:prod
