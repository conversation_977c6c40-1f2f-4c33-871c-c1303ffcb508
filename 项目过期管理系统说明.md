# 项目过期管理系统设计文档

## 概述

为了解决商户表中`expirationDate`字段导致的创建失败问题，我们重新设计了项目过期时间管理系统。新系统将过期时间管理独立出来，提供更灵活和强大的功能。

## 系统架构

### 1. 数据库设计

#### ProjectExpiration 实体
```typescript
@Entity('project_expiration')
export class ProjectExpiration {
  id: string;                    // 主键
  projectName: string;           // 项目名称
  projectDescription?: string;   // 项目描述
  merchantId: string;           // 关联商户ID
  startDate: Date;              // 开始时间
  expirationDate: Date;         // 过期时间
  projectType: string;          // 项目类型(trial/standard/premium/enterprise)
  status: string;               // 状态(active/warning/expired/suspended/canceled)
  reminderDays: number;         // 提醒天数
  autoRenewal: boolean;         // 是否自动续期
  renewalMonths?: number;       // 续期时长(月)
  remarks?: string;             // 备注信息
  lastCheckedAt?: Date;         // 最后检查时间
  createdAt: Date;              // 创建时间
  updatedAt: Date;              // 更新时间
}
```

### 2. API 接口

#### 基础CRUD操作
- `POST /nest/coze/project-expiration/create` - 创建项目配置
- `PUT /nest/coze/project-expiration/update/:id` - 更新项目配置
- `POST /nest/coze/project-expiration/delete` - 删除项目配置
- `POST /nest/coze/project-expiration/find` - 查询项目列表
- `GET /nest/coze/project-expiration/detail/:id` - 获取项目详情

#### 管理功能
- `GET /nest/coze/project-expiration/stats` - 获取统计数据
- `POST /nest/coze/project-expiration/check-status` - 手动检查状态

### 3. 前端界面

#### 功能特性
- 📊 **统计面板**: 显示总项目数、正常、即将过期、已过期等统计
- 📋 **项目列表**: 支持分页、搜索、筛选的项目管理表格
- ✏️ **表单操作**: 创建、编辑、删除项目配置
- 🔔 **状态提醒**: 自动计算剩余天数，状态标识
- 🔄 **状态检查**: 手动或自动检查项目过期状态

#### 界面组件
- 统计卡片组 (总数、正常、警告、过期等)
- 数据表格 (项目信息、状态、操作按钮)
- 创建/编辑弹窗 (表单输入、验证)
- 筛选工具栏 (搜索、刷新、新建)

### 4. 自动化功能

#### 定时任务
```typescript
@Cron(CronExpression.EVERY_DAY_AT_1AM)
async checkProjectExpirationStatus()
```
- 每天凌晨1点自动检查所有项目状态
- 根据剩余天数更新项目状态
- 记录检查时间和状态变更

#### 状态计算逻辑
- `expired`: 已过期 (剩余天数 < 0)
- `warning`: 即将过期 (剩余天数 <= 提醒天数)
- `active`: 正常状态 (剩余天数 > 提醒天数)

## 主要改进

### 1. 解决的问题
- ✅ 移除Merchant表的`expirationDate`字段，解决创建失败问题
- ✅ 独立的过期时间管理，不影响商户基础功能
- ✅ 支持一个商户多个项目的过期时间设置

### 2. 新增功能
- 🎯 **灵活的项目类型**: 试用版、标准版、高级版、企业版
- 📅 **自定义提醒**: 可设置提醒天数
- 🔄 **自动续期**: 支持自动续期功能
- 📊 **统计分析**: 提供详细的过期状态统计
- 🕐 **定时检查**: 自动更新项目状态

### 3. 用户体验
- 🎨 **直观界面**: 清晰的状态标识和剩余天数显示
- 🚀 **快速操作**: 一键创建、编辑、删除配置
- 📱 **响应式设计**: 适配各种屏幕尺寸
- 🔍 **搜索筛选**: 支持多条件查询

## 使用指南

### 1. 创建项目配置
1. 点击"新建项目配置"按钮
2. 填写项目信息（名称、关联商户、有效期等）
3. 设置提醒和续期选项
4. 保存配置

### 2. 管理项目
- **查看状态**: 在列表中查看项目状态和剩余天数
- **编辑配置**: 点击编辑按钮修改项目设置
- **删除项目**: 确认后删除不需要的项目配置
- **状态检查**: 手动触发状态更新

### 3. 监控统计
- **总览面板**: 查看项目总数和各状态分布
- **过期提醒**: 关注即将过期和已过期的项目
- **续期管理**: 处理需要续期的项目

## 技术特点

### 1. 后端技术
- **NestJS**: 企业级Node.js框架
- **TypeORM**: 数据库ORM映射
- **定时任务**: @nestjs/schedule 支持
- **类型安全**: 完整的TypeScript类型定义

### 2. 前端技术
- **React + TypeScript**: 类型安全的前端开发
- **Ant Design**: 企业级UI组件库
- **Pro Layout**: 高级布局组件
- **响应式**: 适配多设备访问

### 3. 数据处理
- **自动计算**: 实时计算剩余天数
- **状态管理**: 自动更新项目状态
- **关系映射**: 支持商户与项目的关联
- **数据验证**: 完整的输入验证机制

## 部署说明

### 1. 后端部署
```bash
# 安装新依赖
npm install @nestjs/schedule

# 数据库迁移会自动创建project_expiration表
npm run start:dev
```

### 2. 前端部署
- 新页面已添加到路由配置
- 访问 `/ProjectExpiration` 即可使用
- 无需额外配置

### 3. 数据迁移
- 系统会自动创建新的数据表
- 现有Merchant数据不受影响
- 可手动将原有过期时间数据迁移到新表

## 注意事项

1. **权限控制**: 只能管理当前用户创建的项目配置
2. **数据一致性**: 删除商户时需要考虑关联的项目配置
3. **性能优化**: 大量项目时考虑分页和索引优化
4. **备份恢复**: 重要的过期时间配置建议定期备份

## 未来扩展

1. **邮件提醒**: 过期前自动发送邮件通知
2. **批量操作**: 支持批量编辑、删除项目
3. **导入导出**: Excel文件的导入导出功能
4. **审计日志**: 记录所有配置变更历史
5. **API集成**: 提供第三方系统集成接口