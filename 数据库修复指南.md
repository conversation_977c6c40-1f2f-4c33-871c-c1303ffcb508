# 数据库连接问题修复指南

## 问题描述
应用启动时出现 `Unknown database 'qf'` 错误，需要创建数据库。

## 解决方案

### 方法一：使用提供的脚本（推荐）

1. **Windows用户**：
   ```bash
   # 双击运行或在命令行执行
   create-database.bat
   ```

2. **手动执行SQL**：
   - 打开MySQL Workbench、phpMyAdmin或其他MySQL客户端
   - 执行 `create-database.sql` 中的SQL命令

### 方法二：使用Docker（推荐）

1. **启动MySQL服务**：
   ```bash
   docker-compose up mysql -d
   ```

2. **验证服务运行**：
   ```bash
   docker-compose ps
   ```

### 方法三：手动创建数据库

1. **启动MySQL服务**：
   ```bash
   # Windows
   net start mysql80
   
   # macOS/Linux
   sudo service mysql start
   ```

2. **连接MySQL并创建数据库**：
   ```sql
   mysql -u root -p
   CREATE DATABASE IF NOT EXISTS qf CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   USE qf;
   EXIT;
   ```

## 验证修复

1. 重启应用：`npm run start:dev`
2. 检查日志，应该不再出现数据库连接错误
3. 访问 API 接口：
   - `GET http://localhost:3010/nest/coze/merchant/findAll`
   - `POST http://localhost:3010/nest/coze/ip/find`

## 更新的改进

### Docker配置优化
- ✅ 添加了MySQL服务到docker-compose.yml
- ✅ 配置了数据库初始化脚本
- ✅ 设置了正确的环境变量

### API接口优化
- ✅ 添加了更好的错误处理
- ✅ 增加了日志记录
- ✅ 提高了代码健壮性

## 常见问题

**Q: 权限被拒绝？**
A: 请以管理员身份运行命令行工具

**Q: MySQL未安装？**
A: 请安装MySQL或使用Docker方式

**Q: 端口冲突？**
A: 修改docker-compose.yml中的端口映射

## 下一步
数据库创建成功后，应用应该能够正常启动并响应API请求。