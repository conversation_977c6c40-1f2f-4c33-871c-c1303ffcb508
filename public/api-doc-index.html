<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API接口文档</title>
  <style>
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #1890ff;
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 10px;
    }
    .card-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 30px;
    }
    .card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 20px;
      width: calc(33.33% - 20px);
      min-width: 300px;
      transition: all 0.3s ease;
    }
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
    .card h3 {
      color: #1890ff;
      margin-top: 0;
      font-size: 18px;
    }
    .card p {
      color: #666;
      margin-bottom: 15px;
    }
    .card a {
      display: inline-block;
      background: #1890ff;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      text-decoration: none;
      font-weight: 500;
      transition: background 0.3s;
    }
    .card a:hover {
      background: #096dd9;
    }
    .intro {
      background: #f6f8fa;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 30px;
    }
    .intro h2 {
      margin-top: 0;
      color: #1890ff;
    }
    code {
      background: #f0f0f0;
      padding: 2px 5px;
      border-radius: 3px;
    }
  </style>
</head>
<body>
  <h1>后端API接口文档中心</h1>

  <div class="intro">
    <h2>欢迎使用API文档</h2>
    <p>本文档提供了后端系统的API接口说明，您可以通过以下不同的视图查看并测试API接口。</p>
    <p>如有使用问题，请参考 <code>src/swagger-readme.md</code> 中的详细说明。</p>
  </div>

  <div class="card-container">
    <div class="card">
      <h3>完整API文档</h3>
      <p>包含所有模块的API文档，提供完整的接口列表和测试功能。</p>
      <a href="/api-docs" target="_blank">查看文档</a>
    </div>

    <div class="card">
      <h3>社交媒体API</h3>
      <p>抖音相关API，包括抖音账号信息获取和视频内容采集功能。</p>
      <a href="/api-docs/social-media" target="_blank">查看文档</a>
    </div>

    <div class="card">
      <h3>用户与认证API</h3>
      <p>用户管理和身份认证相关API，包括登录、注册等功能。</p>
      <a href="/api-docs/auth" target="_blank">查看文档</a>
    </div>

    <div class="card">
      <h3>Knife4j文档</h3>
      <p>使用Knife4j提供的增强UI界面，更美观、功能更强大的API文档。</p>
      <a href="/doc.html" target="_blank">查看文档</a>
    </div>

    <div class="card">
      <h3>API说明</h3>
      <p>查看API使用说明文档，了解接口使用方法和注意事项。</p>
      <a href="javascript:alert('请打开src/swagger-readme.md文件查看详细说明')" target="_blank">查看说明</a>
    </div>
  </div>

</body>
</html> 