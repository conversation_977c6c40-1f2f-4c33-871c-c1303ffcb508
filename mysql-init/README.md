# 数据库迁移脚本

本目录包含了删除痛点管理模块和简化问答库管理的数据库迁移脚本。

## 文件说明

- `migration_remove_pain_simplify_qa.sql` - 主要迁移脚本
- `rollback_qa_structure.sql` - 回滚脚本
- `run_migration.sh` - Linux/Mac 执行脚本
- `run_migration.bat` - Windows 执行脚本

## 迁移内容

### 删除的功能
1. **痛点管理模块**
   - 删除 `pain_entity` 表及其所有数据

### 简化的功能
2. **问答库管理**
   - 删除字段：`category`（问题业务归类）
   - 删除字段：`coreKeywords`（核心关键词）
   - 删除字段：`associatedProducts`（关联产品）
   - 删除字段：`respondsToPpid`（关联痛点ID）
   - 保留字段：`userQuestions`（问题内容）
   - 保留字段：`standardAnswerMarket`（标准回答）

## 使用方法

### 手动执行（推荐）

1. **备份数据库**（必须！）
   ```bash
   mysqldump -u root -p qf > qf_backup_$(date +%Y%m%d).sql
   ```

2. **执行迁移**
   ```bash
   mysql -u root -p < migration_remove_pain_simplify_qa.sql
   ```

### 使用脚本执行

#### Linux/Mac
```bash
# 赋予执行权限
chmod +x run_migration.sh

# 执行迁移
./run_migration.sh

# 回滚（如需要）
./run_migration.sh --rollback
```

#### Windows
```batch
# 执行迁移
run_migration.bat

# 回滚（如需要）
run_migration.bat rollback
```

## 安全提示

⚠️ **重要提示**
1. **执行前必须备份数据库**
2. 建议在测试环境先执行验证
3. 迁移脚本会自动创建 `qa_backup` 表作为备份
4. 回滚脚本依赖于 `qa_backup` 表，请勿随意删除

## 验证迁移结果

迁移完成后，可以执行以下SQL验证：

```sql
-- 检查痛点表是否已删除
SHOW TABLES LIKE 'pain_entity';  -- 应该返回空结果

-- 检查qa表结构
DESCRIBE qa;

-- 检查数据完整性
SELECT COUNT(*) FROM qa;
SELECT COUNT(*) FROM qa_backup;  -- 应该相等
```

## 注意事项

1. 迁移后相关的后端API可能需要调整
2. 前端界面已经相应修改，只显示保留的字段
3. 如果发现问题，可以使用回滚脚本恢复
4. 建议保留备份表一段时间（如1个月）后再删除

## 联系方式

如有问题，请及时反馈。