-- 数据库迁移脚本：删除痛点管理模块，简化问答库管理
-- 创建时间: 2025-01-24
-- 描述: 
--   1. 删除 pain_entity 表及其相关数据
--   2. 简化 qa 表结构，只保留核心字段：userQuestions 和 standardAnswerMarket

USE qf;

-- ==================================================
-- 第一部分：删除痛点管理相关表和数据
-- ==================================================

-- 删除痛点实体表
DROP TABLE IF EXISTS `pain_entity`;

-- ==================================================
-- 第二部分：简化问答库表结构
-- ==================================================

-- 备份原始qa表数据到临时表
CREATE TABLE `qa_backup` AS SELECT * FROM `qa`;

-- 创建新的简化的qa表结构
DROP TABLE IF EXISTS `qa`;
CREATE TABLE `qa` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `merchantId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `userQuestions` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '问题内容',
  `standardAnswerMarket` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标准回答',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_merchantId` (`merchantId`)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '问答库表（简化版）' ROW_FORMAT = DYNAMIC;

-- 迁移数据：只保留核心字段
INSERT INTO `qa` (
  `id`, 
  `created_at`, 
  `created_by`, 
  `updated_at`, 
  `updated_by`, 
  `merchantId`, 
  `userQuestions`, 
  `standardAnswerMarket`
)
SELECT 
  `id`,
  `created_at`,
  `created_by`,
  `updated_at`,
  `updated_by`,
  `merchantId`,
  `userQuestions`,
  `standardAnswerMarket`
FROM `qa_backup`;

-- 验证数据迁移结果
SELECT 
  'qa_backup' as table_name, 
  COUNT(*) as record_count 
FROM `qa_backup`
UNION ALL
SELECT 
  'qa' as table_name, 
  COUNT(*) as record_count 
FROM `qa`;

-- ==================================================
-- 清理工作
-- ==================================================

-- 删除备份表（可选，建议保留一段时间后再删除）
-- DROP TABLE IF EXISTS `qa_backup`;

-- ==================================================
-- 迁移完成提示
-- ==================================================
SELECT 'Migration completed successfully!' as status;