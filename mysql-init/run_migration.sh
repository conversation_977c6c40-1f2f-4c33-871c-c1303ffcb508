#!/bin/bash

# 数据库迁移执行脚本
# 使用方法: ./run_migration.sh [--rollback]

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="zengtao123"
DB_NAME="qf"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD -e "SELECT 1;" >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        log_error "无法连接到MySQL数据库，请检查配置"
        exit 1
    fi
    log_info "MySQL连接正常"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_file="qf_backup_${timestamp}.sql"
    
    mysqldump -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD $DB_NAME > $backup_file
    if [ $? -eq 0 ]; then
        log_info "数据库备份完成: $backup_file"
    else
        log_error "数据库备份失败"
        exit 1
    fi
}

# 执行迁移
run_migration() {
    log_info "开始执行数据库迁移..."
    mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD < migration_remove_pain_simplify_qa.sql
    if [ $? -eq 0 ]; then
        log_info "数据库迁移完成"
    else
        log_error "数据库迁移失败"
        exit 1
    fi
}

# 执行回滚
run_rollback() {
    log_warn "开始执行回滚操作..."
    mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD < rollback_qa_structure.sql
    if [ $? -eq 0 ]; then
        log_info "回滚操作完成"
    else
        log_error "回滚操作失败"
        exit 1
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "数据库迁移脚本"
    echo "========================================"
    
    # 检查参数
    if [ "$1" = "--rollback" ]; then
        log_warn "将执行回滚操作，这将恢复原始表结构"
        read -p "确认继续？(y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            log_info "操作已取消"
            exit 0
        fi
        
        check_mysql_connection
        run_rollback
    else
        log_info "将执行迁移操作：删除痛点管理模块，简化问答库管理"
        read -p "确认继续？(y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            log_info "操作已取消"
            exit 0
        fi
        
        check_mysql_connection
        backup_database
        run_migration
    fi
    
    log_info "操作完成！"
}

# 执行主函数
main "$@"