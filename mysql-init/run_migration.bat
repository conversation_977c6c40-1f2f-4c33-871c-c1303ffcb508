@echo off
REM 数据库迁移执行脚本 (Windows版本)
REM 使用方法: run_migration.bat [rollback]

REM 数据库配置
set DB_HOST=localhost
set DB_PORT=3306
set DB_USER=root
set DB_PASSWORD=zengtao123
set DB_NAME=qf

echo ========================================
echo 数据库迁移脚本
echo ========================================

REM 检查参数
if "%1"=="rollback" (
    echo [警告] 将执行回滚操作，这将恢复原始表结构
    set /p confirm="确认继续？(y/N): "
    if /i not "%confirm%"=="y" (
        echo [信息] 操作已取消
        exit /b 0
    )
    
    echo [信息] 检查MySQL连接...
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
    if errorlevel 1 (
        echo [错误] 无法连接到MySQL数据库，请检查配置
        pause
        exit /b 1
    )
    
    echo [信息] 开始执行回滚操作...
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% < rollback_qa_structure.sql
    if errorlevel 1 (
        echo [错误] 回滚操作失败
        pause
        exit /b 1
    )
    echo [信息] 回滚操作完成
) else (
    echo [信息] 将执行迁移操作：删除痛点管理模块，简化问答库管理
    set /p confirm="确认继续？(y/N): "
    if /i not "%confirm%"=="y" (
        echo [信息] 操作已取消
        exit /b 0
    )
    
    echo [信息] 检查MySQL连接...
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
    if errorlevel 1 (
        echo [错误] 无法连接到MySQL数据库，请检查配置
        pause
        exit /b 1
    )
    
    echo [信息] 备份数据库...
    for /f "tokens=1-4 delims=/ " %%a in ('date /t') do set mydate=%%c%%a%%b
    for /f "tokens=1-2 delims=: " %%a in ('time /t') do set mytime=%%a%%b
    set timestamp=%mydate%_%mytime%
    set backup_file=qf_backup_%timestamp%.sql
    
    mysqldump -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% > %backup_file%
    if errorlevel 1 (
        echo [错误] 数据库备份失败
        pause
        exit /b 1
    )
    echo [信息] 数据库备份完成: %backup_file%
    
    echo [信息] 开始执行数据库迁移...
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% < migration_remove_pain_simplify_qa.sql
    if errorlevel 1 (
        echo [错误] 数据库迁移失败
        pause
        exit /b 1
    )
    echo [信息] 数据库迁移完成
)

echo [信息] 操作完成！
pause