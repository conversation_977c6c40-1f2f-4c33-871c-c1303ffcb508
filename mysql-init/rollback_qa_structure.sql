-- 回滚脚本：恢复问答库表的原始结构
-- 创建时间: 2025-01-24
-- 警告：此脚本仅用于紧急回滚，执行前请确保有完整的数据备份

USE qf;

-- ==================================================
-- 回滚问答库表结构
-- ==================================================

-- 检查备份表是否存在
SELECT 
  CASE 
    WHEN COUNT(*) > 0 THEN 'qa_backup 表存在，可以进行回滚'
    ELSE 'qa_backup 表不存在，无法回滚'
  END as backup_status
FROM information_schema.tables 
WHERE table_schema = 'qf' AND table_name = 'qa_backup';

-- 恢复原始表结构（如果备份表存在）
DROP TABLE IF EXISTS `qa`;
CREATE TABLE `qa` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `merchantId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `respondsToPpid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `userQuestions` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `standardAnswerMarket` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `coreKeywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `associatedProducts` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- 恢复数据
INSERT INTO `qa` SELECT * FROM `qa_backup`;

-- 验证回滚结果
SELECT 
  'qa' as table_name, 
  COUNT(*) as record_count 
FROM `qa`;

SELECT 'Rollback completed!' as status;