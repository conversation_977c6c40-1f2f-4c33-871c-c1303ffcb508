-- 数据库迁移脚本：为所有相关实体添加 _userProjectIds 字段
-- 执行日期：2024年
-- 说明：此脚本为 Material、Qa、Human、Video 实体添加 _userProjectIds 字段以支持项目权限过滤

-- 1. 为 material 表添加 _userProjectIds 字段
ALTER TABLE `material` 
ADD COLUMN `_userProjectIds` TEXT NULL 
COMMENT '用户项目ID列表，用于权限过滤，存储为JSON字符串';

-- 2. 为 qa 表添加 _userProjectIds 字段
ALTER TABLE `qa` 
ADD COLUMN `_userProjectIds` TEXT NULL 
COMMENT '用户项目ID列表，用于权限过滤，存储为JSON字符串';

-- 3. 为 human 表添加 _userProjectIds 字段
ALTER TABLE `human` 
ADD COLUMN `_userProjectIds` TEXT NULL 
COMMENT '用户项目ID列表，用于权限过滤，存储为JSON字符串';

-- 4. 为 video 表添加 _userProjectIds 字段
ALTER TABLE `video` 
ADD COLUMN `_userProjectIds` TEXT NULL 
COMMENT '用户项目ID列表，用于权限过滤，存储为JSON字符串';

-- 验证字段是否添加成功
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND COLUMN_NAME = '_userProjectIds'
    AND TABLE_NAME IN ('material', 'qa', 'human', 'video')
ORDER BY 
    TABLE_NAME;

-- 注意事项：
-- 1. 此字段为可选字段，允许为NULL
-- 2. 字段用于存储JSON格式的项目ID数组，例如：["project1", "project2"]
-- 3. 在应用层面，此字段主要用于权限过滤，不直接参与业务逻辑
-- 4. 执行此脚本前请备份数据库
-- 5. 建议在测试环境先执行并验证无误后再在生产环境执行
