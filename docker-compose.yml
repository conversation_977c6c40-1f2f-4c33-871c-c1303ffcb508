services:
  app:
    container_name: coze-app
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./:/src  # 挂载整个项目目录
      - /usr/src/app/node_modules  # 排除node_modules
    ports:
      - "3558:3010"
    environment:
      - NODE_ENV=production
      - TZ=Asia/Shanghai
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=zengtao123
    depends_on:
      - redis
    networks:
      - coze-network

  redis:
    image: redis:alpine
    container_name: coze-redis
    command: redis-server --requirepass zengtao123
    ports:
      - "3557:6379"
    volumes:
      - redis_data:/data
    networks:
      - coze-network

volumes:
  mysql_data:
  redis_data:

networks:
  coze-network:
    driver: bridge