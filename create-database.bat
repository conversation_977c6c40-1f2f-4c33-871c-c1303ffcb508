@echo off
echo 正在创建MySQL数据库 'qf'...
echo.

:: 检查MySQL是否安装
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: MySQL客户端未安装或不在PATH中
    echo 请安装MySQL或确保mysql命令可用
    echo.
    echo 您也可以：
    echo 1. 使用MySQL Workbench等图形化工具
    echo 2. 手动执行 create-database.sql 中的SQL命令
    echo.
    pause
    exit /b 1
)

:: 创建数据库
echo 请输入MySQL root密码 (默认: zengtao123):
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS qf CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if %errorlevel% equ 0 (
    echo.
    echo ✅ 数据库 'qf' 创建成功！
    echo 现在可以启动应用程序了。
) else (
    echo.
    echo ❌ 数据库创建失败！
    echo 请检查MySQL服务是否运行，以及用户名密码是否正确。
)

echo.
pause